# Cleanup Debug, Development, and Testing Code Command

## Purpose

Remove temporary code created for debugging or development purposes in .NET/C# projects while preserving meaningful comments.

## What to Remove

- Debug output statements (`Console.WriteLine`, `Debug.Log`, `Trace.WriteLine`)
- Code in conditional compilation blocks (`#if DEBUG`, `#if TEST`)
- Test-only methods and classes
- Commented-out code
- Debug-related TODO comments
- Unused test imports and dependencies
- Development-only configuration
- Test fixtures and mock implementations
- Feature flags only used during development

## What to Keep

- Business logic documentation
- API documentation
- Architectural decision comments
- XML documentation comments
- Known limitations documentation

## Temporary Files to Consider

- Local configuration files with dev settings
- Generated test data
- Prototype implementations
- Experimental feature files

## Process

1. Scan project files
2. Identify debug/development code
3. Remove temporary code while preserving important comments
4. Remove development-only files
5. Report changes summary

## Example

### Before

```csharp
/// <summary>Calculates the total price with tax</summary>
public decimal CalculateTotal(List<decimal> prices)
{
  Console.WriteLine($"Input prices: {string.Join(", ", prices)}"); // DEBUG
  
  // TODO: Remove this debug loop
  decimal sum = 0;
  for (int i = 0; i < prices.Count; i++)
  {
    Debug.WriteLine($"Index: {i}, Price: {prices[i]}"); // DEBUG
    sum += prices[i];
  }
  
  // Old implementation
  // return prices.Sum() * TAX_MULTIPLIER;
  
  #if DEBUG
  Console.WriteLine($"Using sum: {sum}");
  Console.WriteLine($"Using tax multiplier: {TAX_MULTIPLIER}");
  #endif
  
  return sum * TAX_MULTIPLIER;
}
```

### After

```csharp
/// <summary>Calculates the total price with tax</summary>
public decimal CalculateTotal(List<decimal> prices)
{
  decimal sum = 0;
  for (int i = 0; i < prices.Count; i++)
  {
    sum += prices[i];
  }
  
  return sum * TAX_MULTIPLIER;
}
```
