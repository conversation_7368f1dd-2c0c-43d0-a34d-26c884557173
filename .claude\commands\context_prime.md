# Context Prime

## This file is used to prime the context for Claude Code

Read README.md and use `git ls-files` to find the files that are relevant to this project.
Read CLAUDE.md and CLAUDE.local.md if needed, if not already in context.
There's a lot of files .cs, so we need to filter them down to the most relevant ones.
filter by .csproj and .sln first one folder depth.
then, read the files and extract the relevant information.
