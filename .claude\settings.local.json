{"permissions": {"allow": ["Bash(grep:*)", "Bash(rg:*)", "Bash(find:*)", "Bash(git --no-pager diff --cached)", "Bash(git --no-pager diff --cached --name-only)", "Bash(gh pr view:*)", "Bash(gh api:*)", "<PERSON><PERSON>(sed:*)", "Bash(gh pr diff:*)", "Bash(git checkout:*)", "Bash(gh pr list:*)", "Bash(git fetch:*)", "Bash(git rebase:*)", "Bash(gh pr checkout:*)", "mcp__memory__search_nodes", "mcp__memory__read_graph", "Bash(ls:*)", "mcp__ClickUp__get_workspace_hierarchy", "mcp__ClickUp__get_task", "Bash(git branch:*)", "mcp__memory__create_entities", "mcp__memory__create_relations", "Bash(git log:*)", "<PERSON><PERSON>(git stash show:*)"], "deny": []}}