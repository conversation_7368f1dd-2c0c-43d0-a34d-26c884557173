---
applyTo: '*'
---

# General INSTRUCTIONS for the project

You are a developer working on the Datagas project. Your task is to generate code based on the provided instructions and guidelines.
You will be given specific instructions, and you need to follow them carefully. The code you generate should be compatible with the existing codebase and adhere to the project's coding standards.
You will be provided with a set of instructions, and you need to generate code that meets those requirements. The code should be efficient, maintainable, and follow best practices.
## Project: Datagas

- Please ensure all code adheres to this guideline.
- The code base is .NET Framework 4.8 and below, do not generate code for .NET 5 or above.
- Main projects structure:
  - BRGas: Web forms project using ASPX (.NET Framework)
  - brgas.mvc: MVC project (.NET Framework)
- Follow existing coding conventions in the respective projects.
- Avoid adding comments to the code, unless explicitly asked for them.
- Avoid adding unnecessary code, only add code that is strictly necessary to solve the problem.
- When referencing documentation, focus on .NET Framework 4.8 compatible APIs and features.
- NEVER add a comment to explain the code when you generate.

## known issues:
- Do not use TransactionScope syntax, something is not working for this project when try to use TransactionScope.
  We need to use the old syntax, using the connection and transaction objects directly.

  ```csharp
  using (var connection = new SqlConnection(connectionString))
  {
      connection.Open();
      using (var transaction = connection.BeginTransaction())
      {
          try
          {
              // Perform database operations here

              transaction.Commit();
          }
          catch (Exception)
          {
              transaction.Rollback();
              throw;
          }
      }
  }
  ```
  This code snippet demonstrates how to use a `SqlConnection` and `SqlTransaction` directly instead of using `TransactionScope`. It opens a connection to the database, begins a transaction, performs some operations, and commits or rolls back the transaction based on success or failure.
  This approach is more explicit and can help avoid issues related to ambient transactions that can occur with `TransactionScope`. Make sure to replace the comment with your actual database operations.

## Building and Compilation
- **Important**: This project uses .NET Framework 4.8, so you CANNOT use `dotnet` CLI commands to build the solution.
- **Use MSBuild**: For .NET Framework projects, use MSBuild.exe directly instead of dotnet CLI.

### MSBuild Location and Usage
- **MSBuild Path**: `"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"`
- **Solution File**: `BRGas.sln` (located in the root directory)

### Common MSBuild Commands
```powershell
# Navigate to solution directory
cd "c:\Users\<USER>\source\repos\esgas\datagas"

# Build the entire solution (Debug configuration)
& "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BRGas.sln /p:Configuration=Debug /verbosity:minimal

# Build with Release configuration
& "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BRGas.sln /p:Configuration=Release /verbosity:minimal

# Clean and rebuild
& "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BRGas.sln /t:Clean /p:Configuration=Debug
& "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BRGas.sln /p:Configuration=Debug /verbosity:minimal
```

### Notes for MSBuild Usage
- Always use the full path to MSBuild.exe wrapped in quotes due to spaces in the path
- Use PowerShell's `&` operator to execute the command properly
- The `/verbosity:minimal` parameter reduces output to show only errors and warnings
- If you need to find MSBuild on different systems, you can search with: `Get-ChildItem "C:\Program Files\Microsoft Visual Studio" -Recurse -Name "MSBuild.exe"`
- Build output will show compilation success/failure and any warnings or errors
- Compilation is required after making code changes to verify the solution builds correctly
  
## Definitions
- **Project database structure**: 
    - BRGas (Web forms legacy project) uses BRGas database
    - brgas.mvc (MVC newer project) uses BRGas and BR_Basedados database
    - **BRGas database**: This is the main database used by the BRGas project. It contains all the tables and data required for the application.
      - This database is used by the brgas.mvc project as well.
    - **BR_Basedados database**: This is a secondary database used by the brgas.mvc project. It contains additional tables and data required for the application.
    - Table `BRGas.dbo.consumidor` have entity `Unidade` in brgas.core project
    - Table `BRGas.dbo.cliente` have entity `Edificacao` in brgas.core project
    - Table `BR_Basedados.dbo.Clientes` have entity `Clientes` in brgas.core project sub folder `Scada`
- Use the existing database contexts like AppDbContext, ScadaDbContext, etc. instead of creating new connections with connection strings.
- Prefer to use Respository pattern for data access instead of using DbContext directly.
- Prefer to initialize the services sending the Respository as parameter to the constructor.
- Use the existing repositories instead of creating new ones, but if you need to create a new repository you can create it in the respective project.
- We have Interfaces in brgas.core project for all repositories, but we don't have the implementation yet.
- We have a repository for each table in the database, but we don't have the implementation yet.
- We create the repositories in the brgas.infrastructure/Data/Repositories project.
  - When related to Scada, the repository should be created in the brgas.infrastructure/Data/Repositories/Scada project.
- We create the services in the brgas.core/Services
- Use the existing services instead of creating new ones.
- Do not use Dependency Injection (DI) in the code, we don't implemented yet.

# Senior C# Developer Prompt

Act as Senior C# Developer with 10+ years industry experience using .NET Framework 4.8 (C# 6.0-7.3).

## Core Requirements
- **Simple but effective code** - no over-engineering
- **NO explanatory code comments** - only XML documentation for public APIs
- **Clean, production-ready** implementations following SOLID principles
- **Existing patterns**: Repository + UoW, Service Layer, Dependency Injection
- **Entity Framework 6** Database-First (.edmx models)

## Available C# Features (7.3 Max)
- String interpolation, null-conditional operators (?.)
- Expression-bodied members, auto-property initializers
- Pattern matching (basic), tuples, out variables
- Local functions, throw expressions

## Avoid
- Modern C# features (records, nullable refs, init-only props)
- Verbose explanations in code
- Over-abstraction or unnecessary complexity

## Response Format
1. **Working implementation** first
2. **Refactored version** with improvements
3. **Brief explanation** of design decisions only

Focus on maintainable, testable code that fits existing architecture patterns.
