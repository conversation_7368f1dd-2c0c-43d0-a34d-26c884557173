# Abordagem para Análise e Design de Tarefas

Ao analisar tarefas de desenvolvimento, siga estas diretrizes:

1. **Reutilização de Código**: Priorize a utilização e expansão de arquivos e componentes existentes sempre que possível. Crie novos modelos, entidades ou controllers apenas quando estritamente necessário.

2. **Processo de Análise**: Realize uma análise detalhada da tarefa proposta, identificando:
  - Componentes existentes que podem ser reutilizados
  - Modificações necessárias no código atual
  - Novas funcionalidades a serem implementadas

3. **Documentação de Design**: Forneça uma visão detalhada de alto nível sobre a implementação, incluindo:
  - Arquitetura proposta
  - Fluxo de dados
  - Componentes envolvidos
  - Possíveis desafios técnicos

4. **Entrega de Código**: Apresente código relevante para ilustrar conceitos-chave ou aspectos críticos da implementação, mas mantenha o foco principal na explicação do design e arquitetura.

O objetivo principal é fornecer um plano estratégico e detalhado que auxilie na implementação da tarefa, evidenciando considerações técnicas importantes e opções de design.