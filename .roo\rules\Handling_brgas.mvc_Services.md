# Handling BRGas.MVC Services

Based on the project structure you've described, here's how to best handle services in your application:

## Service Layer Architecture

For the brgas.mvc project, I recommend implementing services following these guidelines:

Service Interfaces in brgas.core

```C#
namespace brgas.core.Services
{
    public interface IClienteService
    {
        Task<Edificacao> GetClienteByIdAsync(int id);
        Task<IEnumerable<Edificacao>> GetAllClientesAsync();
        Task<bool> CreateClienteAsync(Edificacao cliente);
        Task<bool> UpdateClienteAsync(Edificacao cliente);
    }
}
```

2. Service Implementations in brgas.infrastructure

```C#

namespace brgas.infrastructure.Services
{
    public class ClienteService : IClienteService
    {
        private readonly AppDbContext _context;
        
        public ClienteService(AppDbContext context)
        {
            _context = context;
        }
        
        public async Task<Edificacao> GetClienteByIdAsync(int id)
        {
            return await _context.Edificacoes.FindAsync(id);
        }
        
        // Implement other methods
    }
}
```

3. For SCADA-Related Operations

```C#
namespace brgas.infrastructure.Services
{
    public class ScadaIntegrationService : IScadaIntegrationService
    {
        private readonly AppDbContext _brgasContext;
        private readonly ScadaDbContext _scadaContext;
        
        public ScadaIntegrationService(AppDbContext brgasContext, ScadaDbContext scadaContext)
        {
            _brgasContext = brgasContext;
            _scadaContext = scadaContext;
        }
        
        public async Task<ScadaReadingResult> GetScadaReadingsAsync(int clienteId)
        {
            // Implementation that uses both contexts
        }
    }
}
```

### Service Registration

In your Global.asax.cs or a separate DI configuration file:

```C#
protected void Application_Start()
{
    // Other startup code
    
    // Register services
    var container = new UnityContainer();
    container.RegisterType<IClienteService, ClienteService>();
    container.RegisterType<IScadaIntegrationService, ScadaIntegrationService>();
    
    // Set the dependency resolver
    DependencyResolver.SetResolver(new UnityDependencyResolver(container));
}
```

### Using Services in Controllers

```C#
namespace brgas.mvc.Controllers
{
    public class ClienteController : BaseController
    {
        private readonly IClienteService _clienteService;
        
        public ClienteController(IClienteService clienteService)
        {
            _clienteService = clienteService;
        }
        
        public async Task<ActionResult> Index()
        {
            var clientes = await _clienteService.GetAllClientesAsync();
            return View(clientes);
        }
        
        [HttpPost]
        public async Task<ActionResult> Create(ClienteViewModel model)
        {
            if (!ModelState.IsValid)
                return View(model);
                
            var cliente = new Edificacao
            {
                Nome = model.Nome,
                LogradouroId = model.LogradouroId,
                // Map other properties
            };
            
            await _clienteService.CreateClienteAsync(cliente);
            return RedirectToAction("Index");
        }
    }
}
```

### Direct Database Operation Pattern

For services that need direct database operations:

```C#
public async Task<bool> CreateClienteWithTransactionAsync(Edificacao cliente)
{
    string connectionString = ConfigurationManager.ConnectionStrings["BRGAS"].ConnectionString;
    
    using (var connection = new SqlConnection(connectionString))
    {
        connection.Open();
        using (var transaction = connection.BeginTransaction())
        {
            try
            {
                var command = connection.CreateCommand();
                command.Transaction = transaction;
                command.CommandText = "INSERT INTO CLIENTE (CL_NOME, CL_CD_LOGRADOURO) VALUES (@Nome, @LogradouroId); SELECT SCOPE_IDENTITY()";
                command.Parameters.AddWithValue("@Nome", cliente.Nome);
                command.Parameters.AddWithValue("@LogradouroId", cliente.LogradouroId);
                
                cliente.Id = Convert.ToInt32(await command.ExecuteScalarAsync());
                
                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }
    }
}
```

### SCADA Integration Approach

Since you want to minimize direct dependencies on scada projects, consider:

Creating adapter interfaces in brgas.core
Implementing those adapters in brgas.infrastructure that use scada.core/infrastructure
Your main services depend only on the adapters, not directly on SCADA components
This helps maintain separation while still leveraging the SCADA functionality when needed.
