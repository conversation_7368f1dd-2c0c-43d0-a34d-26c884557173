# Project brgas.mvc Overview

The project is a .NET Framework 4.8 MVC application (brgas.mvc) that appears to be part of a gas management system. It follows standard MVC architecture with some customizations:

1. Namespace Structure: All code is under the brgas.mvc namespace with appropriate sub-namespaces
2. Controllers Organization: Controllers are organized by domain entities (Ativos, Leitura, Manifestacao, etc.)
3. Areas: The application is divided into functional areas:
   - Atendimento (Customer Service)
   - Financeiro (Financial)
   - Suporte (Support)
4. ViewModels: Extensive use of view-specific models in the ViewModels folder
5. Validators: FluentValidation for input validation

## Key Coding Patterns

Based on the codebase analysis, I've identified several patterns:

1. Inheritance Structure:

    - Many controllers inherit from BaseController or BaseControllerScada
    - This suggests common functionality is abstracted into base classes

2. Data Access:

    - Direct use of SQL connections and transactions (as noted in instructions)
    - Avoidance of TransactionScope due to project-specific issues

3. View Model Pattern:

    - Strong separation between domain models and presentation models
    - Specific ViewModels for different operations (Create, Edit, Index)

## Recommendations for LLM Code Generation

For effective LLM code generation in this project, I recommend the following approach:

1. Follow Existing Controller Structure:

```C#
namespace brgas.mvc.Controllers
{
    public class EntityNameController : BaseController
    {
        // Action methods with appropriate HTTP verbs
        // Use of view models for data transfer to views
    }
}
```

2. Data Access Pattern:

```C#
using (var connection = new SqlConnection(connectionString))
{
    connection.Open();
    using (var transaction = connection.BeginTransaction())
    {
        try
        {
            // Database operations
            transaction.Commit();
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }
}
```

3. ViewModel Usage:

    - Create dedicated view models for different operations
    - Follow naming convention: EntityNameViewModel, EntityNameCreateViewModel, etc.
    - Place in brgas.mvc.ViewModels namespace

4. Validation:

    - Use FluentValidation in the Validators folder
    - Follow naming convention: EntityNameValidator

5. Areas:

    - Place area-specific controllers in the appropriate area namespace
    - Use area registration for new areas
