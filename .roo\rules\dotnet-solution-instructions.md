# General INSTRUCTIONS to always follow when generating code

- Please ensure all code adheres to this guideline.
- The code base is .NET Framework 4.8 and below, do not generate code for .NET 5 or above.
- Main projects structure:
  - BRGas: Web forms project using ASPX (.NET Framework)
  - brgas.mvc: MVC project (.NET Framework)
- Follow existing coding conventions in the respective projects.
- Avoid adding comments to the code, unless explicitly asked for them.
- Avoid adding unnecessary code, only add code that is strictly necessary to solve the problem.
- When referencing documentation, focus on .NET Framework 4.8 compatible APIs and features.
- NEVER add a comment to explain the code when you generate.

known issues:

- Do not use TransactionScope syntax, something is not working for this project when try to use TransactionScope.
  We need to use the old syntax, using the connection and transaction objects directly.

  ```csharp
  using (var connection = new SqlConnection(connectionString))
  {
      connection.Open();
      using (var transaction = connection.BeginTransaction())
      {
          try
          {
              // Perform database operations here

              transaction.Commit();
          }
          catch (Exception)
          {
              transaction.Rollback();
              throw;
          }
      }
  }
  ```

  This code snippet demonstrates how to use a `SqlConnection` and `SqlTransaction` directly instead of using `TransactionScope`. It opens a connection to the database, begins a transaction, performs some operations, and commits or rolls back the transaction based on success or failure.
  This approach is more explicit and can help avoid issues related to ambient transactions that can occur with `TransactionScope`. Make sure to replace the comment with your actual database operations.
  
Definitions:

- Project database structure:
  - BRGas (Web forms legacy project) uses BRGas database
  - brgas.mvc (MVC newer project) uses BRGas and BR_Basedados database
