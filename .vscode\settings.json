{"lineCountExplorer.countMode": "tokens", "lineCountExplorer.supportedExtensions": [".txt", ".md", ".json", ".py", ".js"], "lineCountExplorer.thresholds": [{"value": 0, "indicator": "⚪", "description": "Tiny size"}, {"value": 2000, "indicator": "🔵", "description": "2K token context"}, {"value": 4000, "indicator": "🟢", "description": "4K token context"}, {"value": 8000, "indicator": "🟡", "description": "8K token context"}, {"value": 16000, "indicator": "🟠", "description": "16K token context"}, {"value": 32000, "indicator": "🔴", "description": "32K token context"}, {"value": 64000, "indicator": "⛔", "description": "Exceeds most context windows"}], "lineCountExplorer.selectedPreset": "llm-context", "lineCountExplorer.indicatorSymbolSet": "Colored Circles", "lineCountExplorer.enabled": true, "dbcode.connections": [{"connectionId": "cnt836uPk9Xt_yFBzHJlZ", "name": "BRGas AWS SQL Server", "driver": "mssql", "host": "sql-server-staging-aws", "port": 3341, "ssl": true, "sslTrustCertificate": true, "driverOptions": {"authType": "sql"}, "username": "coletor", "password": "", "savePassword": "secretStorage", "database": "BRgas", "readOnly": false, "role": "development", "connectionTimeout": 30, "connectionType": "host"}], "dotnet.preferCSharpExtension": true}