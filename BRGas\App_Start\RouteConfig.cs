﻿using System.Web.Mvc;
using System.Web.Routing;

namespace BRGas
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            //routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            //routes.MapPageRoute("Default", "", "~/default.aspx");

            //routes.MapRoute(
            //    name: "DefaultMVC",
            //    url: "{controller}/{action}/{id}",
            //    defaults: new { Controller = "Ativacao", Action = "Index", id = UrlParameter.Optional });

            //routes.MapRoute(
            //    name: "Reserva",
            //    url: "Reserva/{action}/{id}",
            //    defaults: new {Controller = "Reserva", action = "Index", id = UrlParameter.Optional }
            //);

            //routes.MapRoute(
            //    name: "Ativacao",
            //    url: "Ativacao/{action}/{id}",
            //    defaults: new { Controller = "Ativacao", action = "Index", id = UrlParameter.Optional });

            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");
            routes.IgnoreRoute("");

            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{id}",
                defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional }
            );
        }
    }
}
