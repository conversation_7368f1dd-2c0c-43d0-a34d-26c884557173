﻿using BRGas.Models;
using BRGas.ViewModels;
using OfficeOpenXml;
using Rotativa.Options;
using System;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Web;
using System.Web.Helpers;
using System.Web.Mvc;

namespace BRGas.Controllers
{

    public class MalaDiretaController : Controller
    {
        protected override void OnException(ExceptionContext filterContext)
        {
            filterContext.ExceptionHandled = true;

            //Log the error!!
            //_Logger.Error(filterContext.Exception);

            var erro = new ErrorViewModel
            {
                ActionName = ControllerContext.RouteData.Values["action"].ToString(),
                ControllerName = filterContext.Controller.ToString(),
                Exception = filterContext.Exception.ToString()
            };

            //Redirect or return a view, but not both.
            //filterContext.Result = RedirectToAction("Index", "ErrorHandler");

            filterContext.Result = View("ErrorHandler", erro);
            // OR 

            //filterContext.Result = new ViewResult
            //{
            //    ViewName = "~/Views/ErrorHandler/Index.cshtml"
            //};
        }

        public ActionResult Index(string tipoMalaDireta, DateTime? dataVencimento, string cliente)
        {
            ViewBag.habilitarEnviarTodos = true; // false;
            ViewBag.TipoMalaDireta = tipoMalaDireta;
            ViewBag.DataVencimento = dataVencimento;
            ViewBag.Titulo = tipoMalaDireta == "C" ? "Mala Direta - Consumo" : "Mala Direta - Serviço";

            using (var ctx = new BRGasContext())
            {
                var model = ctx.MalaDireta.Where(m => m.Tipo == tipoMalaDireta).AsQueryable();
                long sap;
                if (long.TryParse(cliente, out sap))
                {
                    model = model.Where(m => m.Sap == sap);
                }
                else if (!string.IsNullOrWhiteSpace(cliente))
                {
                    model = model.Where(m => m.RazaoSocial.Contains(cliente));
                }
                else if (dataVencimento != null)
                {
                    //ViewBag.habilitarEnviarTodos = true;
                    model = model.Where(m => m.DataVencimento == dataVencimento);
                }

                model = model.OrderByDescending(m => m.DataVencimento);

                return View(model.ToList());
            }
        }

        public ActionResult Upload()
        {
            return View();
        }

        public static class HtmlToPdf
        {
            public static Attachment GetAttachFile(byte[] data, string fileName)
            {
                MemoryStream ms = new MemoryStream(data);
                var attachment = new Attachment(ms, fileName, "application/pdf");
                return attachment;
            }
        }

        public ActionResult Pesquisar(string filtro)
        {
            if (string.IsNullOrEmpty(filtro))
            {
                return View();
            }

            using (var ctx = new BRGasContext())
            {
                var clientes = ctx.CLIENTE
                    .Where(c => c.CL_AREA != "REPROVADO" &&
                                c.CL_GIS.Contains(filtro.ToUpper()) ||
                                c.CL_NOME.Contains(filtro.ToUpper()))
                    .OrderBy(c => c.CL_GIS).ToList();
                return View(clientes);
            }
        }

        public ActionResult Consumidor(int clienteId, string unidadeUsuaria)
        {
            @ViewBag.UnidadeUsuaria = unidadeUsuaria;
            using (var ctx = new BRGasContext())
            {
                var model = ctx.Consumidor.Where(c => c.CO_CD_CLIENTE == clienteId).OrderBy(c => c.CO_NOME);

                return View(model.ToList());
            }
        }

        public ActionResult ContratoAdesao(int clienteId, int consumidorId, string unidadeUsuaria, string email)
        {
            using (var ctx = new BRGasContext())
            {
                var consumidor = ctx.Consumidor.FirstOrDefault(c => c.CD_CONSUMIDOR == consumidorId);

                if (consumidor != null)
                {
                    var model = new ContratoAdesaoPdf
                    {
                        Id = consumidorId,
                        Email = email,
                        NomeTitular = consumidor.CO_NOME,
                        CPFouCNPJ = consumidor.CO_NUM_DOC,
                        Sap = consumidor.Sap,
                        SapEmissor = consumidor.CO_SAP_EMISSOR
                    };
                    var endereco = ctx.Endereco.FirstOrDefault(e => e.Id == consumidor.CO_CD_CLIENTE);
                    if (endereco != null)
                    {
                        model.Endereco = $"{endereco.TipoLogradouro} {endereco.Logradouro}, {endereco.Numero}";
                        if (endereco.Complemento != null)
                        {
                            model.Endereco += $", {endereco.Complemento}";
                        }
                        model.Endereco += $", { endereco.Bairro}, { endereco.Municipio}/{ endereco.Estado} - { endereco.Cep}";
                    }
                    PrintContratoAdesao(model);

                    if (consumidor.CO_DT_ENVIO_TERMO_ADESAO == null)
                    {
                        consumidor.CO_DT_ENVIO_TERMO_ADESAO = DateTime.Now;
                        ctx.SaveChanges();
                    }
                }
            }
            return RedirectToAction("Consumidor", new { clienteId, unidadeUsuaria });
        }

        public class ContratoAdesaoPdf
        {
            public int Id { get; set; }
            public string Email { get; set; }
            public string NomeTitular { get; set; }
            public string CPFouCNPJ { get; set; }
            public string Endereco { get; set; }
            public long? Sap { get; set; }
            public long? SapEmissor { get; set; }
        }

        public void PrintContratoAdesao(ContratoAdesaoPdf model)
        {
            var body = GetBody(model);

            //var actionPdf = new Rotativa.PartialViewAsPdf("ContratoAdesao", model)
            //var actionPdf = new Rotativa.ActionAsPdf("ContratoAdesao", new { id })
            var actionPdf = new Rotativa.ViewAsPdf("ContratoAdesao", model)
            {
                FileName = "ContratoAdesao.pdf",
                PageSize = Size.A4,
                PageOrientation = Orientation.Portrait,
                PageMargins = { Left = 0, Right = 0 },
            };

            byte[] applicationPdfData = actionPdf.BuildFile(ControllerContext);

            var attachment = HtmlToPdf.GetAttachFile(applicationPdfData, "ContratoAdesao.pdf");

            var mailTo = ConfigurationManager.AppSettings["MailToTest"];
            if (string.IsNullOrWhiteSpace(mailTo))
            {
                mailTo = model.Email;
            }

            var objModelMail = new MailModel()
            {
                To = mailTo,
                Body = body,
                Subject = "Contrato Adesão Gás Canalizado ES Gás"
            };

            var from = ConfigurationManager.AppSettings["MailFrom"];
            using (var mail = new MailMessage(from, objModelMail.To))
            {
                mail.Subject = objModelMail.Subject;
                mail.Body = objModelMail.Body;

                mail.Attachments.Add(attachment);

                mail.IsBodyHtml = false;

                if (!string.IsNullOrWhiteSpace(ConfigurationManager.AppSettings["MailBcc"]))
                {
                    var bcc = new MailAddress(ConfigurationManager.AppSettings["MailBcc"]);
                    mail.Bcc.Add(bcc);
                }

                var smtp = new SmtpClient
                {
                    Host = ConfigurationManager.AppSettings["MailServer"]
                };
                    smtp.EnableSsl = true;
                    smtp.UseDefaultCredentials = true;
                    smtp.Credentials = new NetworkCredential(from, ConfigurationManager.AppSettings["MailPassword"]);
                    smtp.Port = Convert.ToInt32(ConfigurationManager.AppSettings["MailPort"]);
                    smtp.Send(mail);
            }
        }

        private static string GetBody(ContratoAdesaoPdf model)
        {
            return "Prezado cliente, bem-vindo!" +
                   Environment.NewLine +
                   Environment.NewLine +
                   "O seu código de cliente junto a ES Gás é: " +
                   Environment.NewLine +
                   Environment.NewLine +
                   "Código do Ponto de Consumo: " + model.Sap.ToString() + ", Código do Emissor: " + model.SapEmissor.ToString() +
                   Environment.NewLine +
                   Environment.NewLine +
                   "Com o intuito de aprimorar o relacionamento com nossos clientes, disponibilizamos um canal de autoatendimento pela internet com as seguintes funcionalidades: " +
                   Environment.NewLine +
                   "             - retirar 2ª via de nota fiscal e boleto atualizado para pagamento; " +
                   Environment.NewLine +
                   "             - obter o detalhamento de sua fatura; " +
                   Environment.NewLine +
                   "             - obter extrato e/ ou posição financeira; " +
                   Environment.NewLine +
                   "             - conferir as tarifas do gás natural." +
                   Environment.NewLine +
                   Environment.NewLine +
                   "-> Acesse o site www.br.com.br/gasnatural e clique em Serviços Online." +
                   Environment.NewLine +
                   "-->> Informe o seu número de cliente e CPF/ CNPJ." +
                   Environment.NewLine +
                   Environment.NewLine +
                   "Disponibilizamos também o serviço de envio de faturas por e-mail. O e-mail informado em seu pedido de ligação já se encontra cadastrado para o envio.Caso deseja alterá-lo, a solicitação poderá ser realizada através do telefone 0800 095 0197 ou pelo e-mail <EMAIL>, com a descrição detalhada do e-mail a ser cadastrado e/ou alterado." +
                   Environment.NewLine +
                   Environment.NewLine +
                   "Aproveitamos a oportunidade para o envio do contrato de adesão referente a prestação de serviços de distribuição de gás canalizado." +
                   Environment.NewLine +
                   Environment.NewLine +
                   "Mais uma vez agradecemos, colocando-nos desde já à disposição para novos contatos." +
                   Environment.NewLine +
                   Environment.NewLine +
                   "Atenciosamente" +
                   Environment.NewLine +
                   "Equipe de atendimento" +
                   Environment.NewLine +
                   Environment.NewLine +
                   "ES GÁS – COMPANHIA DE GÁS DO ESPÍRITO SANTO" +
                   Environment.NewLine +
                   "Atendimento ao Usuário de Gás Natural" +
                   Environment.NewLine +
                   "Central de atendimento: 0800-595-0197" +
                   Environment.NewLine +
                   "E-mail: <EMAIL>" +
                   Environment.NewLine +
                   Environment.NewLine +
                   "- Agência de Atendimento: Shopping Boulevard da Praia(QUARTAS-FEIRAS – 09:00 ÀS 17:00)" +
                   Environment.NewLine +
                   "Avenida Nossa Senhora da Penha, 356 / ljs 30 e 31" +
                   Environment.NewLine +
                   "Praia do Canto, Vitória – ES" +
                   Environment.NewLine +
                   Environment.NewLine +
                   "- Agência de Atendimento: Edifício Atlântico Sul(TERÇAS E SEXTAS-FEIRAS – 09:00 ÀS 17:00)" +
                   Environment.NewLine +
                   "Rua Humberto Serrano, 99 / lj 01" +
                   Environment.NewLine +
                   "Praia da Costa, Vila Velha – ES" +
                   Environment.NewLine +
                   Environment.NewLine +
                   "- Agência de atendimento: Laranjeiras Shopping(SEGUNDAS E QUINTAS-FEIRAS – 09:00 ÀS 13:30 e 14:30 ÀS 16:00)" +
                   Environment.NewLine +
                   "Av. Primeira avenida, 231" +
                   Environment.NewLine +
                   "Laranjeiras, Serra – ES";
        }

        public class MailModel
        {
            public string To { get; set; }
            public string Subject { get; set; }
            public string Body { get; set; }
        }

        public class GMailer
        {
            public static string GmailUsername { get; set; }
            public static string GmailPassword { get; set; }
            public static string GmailHost { get; set; }
            public static int GmailPort { get; set; }
            public static bool GmailSSL { get; set; }

            public string ToEmail { get; set; }
            public string Subject { get; set; }
            public string Body { get; set; }
            public bool IsHtml { get; set; }

            public void Send()
            {
                SmtpClient smtp = new SmtpClient();
                smtp.Host = GmailHost;
                smtp.Port = GmailPort;
                smtp.EnableSsl = GmailSSL;
                smtp.DeliveryMethod = SmtpDeliveryMethod.Network;
                smtp.UseDefaultCredentials = false;
                smtp.Credentials = new NetworkCredential(GmailUsername, GmailPassword);

                using (var message = new MailMessage(GmailUsername, ToEmail))
                {
                    message.Subject = Subject;
                    message.Body = Body;
                    message.IsBodyHtml = IsHtml;
                    smtp.Send(message);
                }
            }
        }

        [HttpPost]
        public ActionResult Upload(FormCollection formCollection)
        {
            var dataVencimento = string.Empty;
            var tipoMalaDireta = formCollection["tipoMalaDireta"];
            HttpPostedFileBase file = Request?.Files["UploadedFile"];

            if (file != null && (file.ContentLength > 0) && !string.IsNullOrEmpty(file.FileName))
            {
                var fileName = file.FileName;
                var fileContentType = file.ContentType;
                byte[] fileBytes = new byte[file.ContentLength];
                var data = file.InputStream.Read(fileBytes, 0, Convert.ToInt32(file.ContentLength));
                //var usersList = new List<CobrancaCliente>();
                using (var package = new ExcelPackage(file.InputStream))
                {
                    var currentSheet = package.Workbook.Worksheets;
                    var workSheet = currentSheet.First();
                    var noOfCol = workSheet.Dimension.End.Column;
                    var noOfRow = workSheet.Dimension.End.Row;

                    using (var ctx = new BRGasContext())
                    {
                        //ctx.MalaDireta.RemoveRange(ctx.MalaDireta.Where(m => m.Tipo == tipoMalaDireta));

                        for (var rowIterator = 2; rowIterator <= noOfRow; rowIterator++)
                        {
                            MalaDireta cliente = new MalaDireta();
                            var codigoCliente = workSheet.Cells[rowIterator, 1]?.Value;
                            if (codigoCliente != null)
                            {
                                cliente.Tipo = tipoMalaDireta;
                                cliente.Sap = Convert.ToInt32(codigoCliente);
                                cliente.RazaoSocial = workSheet.Cells[rowIterator, 2].Value.ToString();
                                cliente.Email = workSheet.Cells[rowIterator, 3].Value.ToString();
                                cliente.Referencia = workSheet.Cells[rowIterator, 4].Value.ToString();
                                cliente.DataVencimento = Convert.ToDateTime(workSheet.Cells[rowIterator, 5].Value);
                                cliente.Valor = Convert.ToDecimal(workSheet.Cells[rowIterator, 6].Value);

                                dataVencimento = cliente.DataVencimento.ToShortDateString();

                                ctx.MalaDireta.Add(cliente);
                            }
                        }
                        ctx.SaveChanges();
                        ctx.MalaDiretaAtualizarEmails(tipoMalaDireta);
                    }
                }
            }
            return RedirectToAction("Index", new { tipoMalaDireta, dataVencimento });
        }

        public ActionResult EnviarMalaDireta(string tipoMalaDireta, DateTime dataVencimento)
        {
            using (var ctx = new BRGasContext())
            {
                ctx.EnviarMalaDireta(tipoMalaDireta, dataVencimento);
            }

            return RedirectToAction("Index", new { tipoMalaDireta, dataVencimento });
        }

        public ActionResult EmailCobranca(int id)
        {
            using (var ctx = new BRGasContext())
            {
                var model = ctx.MalaDireta.FirstOrDefault(m => m.Id == id);
                return View(model);
            }
        }

        public ActionResult EnviarCobrancaIndividual(string tipoMalaDireta, string id)
        {
            using (var ctx = new BRGasContext())
            {
                ctx.EnviarMalaDiretaIndividual(tipoMalaDireta, Convert.ToInt32(id));
            }

            return RedirectToAction("EmailCobranca", new { id });
        }

        // GET: Home  
        public ActionResult SendEmail()
        {
            var model = new TestMailViewModel
            {
                SmtpServer = "smtpin.br-petrobras.com.br",
                SmtpPort = 25,
                UserName = "<EMAIL>"
            };
            return View(model);
        }

        [HttpPost]
        public ActionResult SendEmail(TestMailViewModel obj)
        {
            try
            {
                //Configuring webMail class to send emails  
                //gmail smtp server  
                WebMail.SmtpServer = obj.SmtpServer;
                //gmail port to send emails  
                WebMail.SmtpPort = obj.SmtpPort;
                WebMail.SmtpUseDefaultCredentials = true;
                //sending emails with secure protocol  
                WebMail.EnableSsl = false;
                //EmailId used to send emails from application  
                WebMail.UserName = obj.UserName;
                WebMail.Password = obj.Password;

                //Sender email address.  
                WebMail.From = obj.UserName;

                //Send email  
                WebMail.Send(to: obj.ToEmail, subject: obj.EmailSubject, body: obj.EMailBody, cc: obj.EmailCC, bcc: obj.EmailBCC, isBodyHtml: true);
                ViewBag.Status = "Email enviado com sucesso.";
            }
            catch (Exception ex)
            {
                ViewBag.Status = "Problema ao enviar o email. Favor verificar dados e configuração.";
                ViewBag.exception = ex;
            }
            return View();
        }

        public ActionResult Excluir()
        {
            using (var ctx = new BRGasContext())
            {
                var model = ctx.MalaDireta.Where(m => !m.DataEnvio.HasValue).ToList();
                return View(model);
            }
        }

        public ActionResult ExcluirItem(int id)
        {
            using (var ctx = new BRGasContext())
            {
                var reg = ctx.MalaDireta.First(m => m.Id == id);
                ctx.MalaDireta.Remove(reg);
                ctx.SaveChanges();
                return RedirectToAction("Excluir");
            }
        }

        public ActionResult ExcluirTodos()
        {
            using (var ctx = new BRGasContext())
            {
                var reg = ctx.MalaDireta.Where(m => !m.DataEnvio.HasValue).ToList();
                ctx.MalaDireta.RemoveRange(reg);
                ctx.SaveChanges();
                return View("Excluir");
            }
        }
    }
}

