namespace BRGas.Migrations
{
    using System.Data.Entity.Migrations;

    internal sealed class Configuration : DbMigrationsConfiguration<BRGas.Models.BRGasContext>
    {
        public Configuration()
        {
            AutomaticMigrationsEnabled = false;
        }

        protected override void Seed(BRGas.Models.BRGasContext context)
        {
            //  This method will be called after migrating to the latest version.

            //  You can use the DbSet<T>.AddOrUpdate() helper extension method 
            //  to avoid creating duplicate seed data. E.g.
            //
            //    context.People.AddOrUpdate(
            //      p => p.FullName,
            //      new Person { FullName = "<PERSON>" },
            //      new Person { FullName = "<PERSON><PERSON><PERSON>" },
            //      new Person { FullName = "<PERSON>" }
            //    );
            //

            //context.Reservas.AddOrUpdate(
            //    p => new { p.rese_cd_cliente, p.rese_came_nr_serie },
            //    new Models.Reserva { rese_cd_cliente = 162490, rese_came_nr_serie = "B15I0003641D" },
            //    new Models.Reserva { rese_cd_cliente = 162490, rese_came_nr_serie = "B15I0003642D" }
            //);
        }
    }
}
