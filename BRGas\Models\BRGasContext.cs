using System;
using System.Data;
using System.Data.Entity;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Data.SqlClient;

namespace BRGas.Models
{
    public partial class BRGasContext : DbContext
    {
        public BRGasContext()
            : base("name=BRGas")
        {
        }
        public virtual DbSet<Endereco> Endereco { get; set; }
        public virtual DbSet<Consumidor> Consumidor { get; set; }

        public virtual DbSet<CLIENTE> CLIENTE { get; set; }
        public virtual DbSet<MalaDireta> MalaDireta { get; set; }

        public void MalaDiretaAtualizarEmails(string tipo)
        {
            SqlParameter[] parameters =
            {
                new SqlParameter("Tipo", SqlDbType.Char, 1)
            };
            parameters[0].Value = tipo;
            Database.ExecuteSqlCommand("EXEC dbo.MalaDiretaAtualizarEmail @Tipo", parameters);
        }

        public void EnviarMalaDireta(string tipo, DateTime vencimento)
        {
            SqlParameter[] parameters =
            {
                new SqlParameter("@DataVencimento", SqlDbType.Date)
            };

            parameters[0].Value = vencimento;

            Database.ExecuteSqlCommand(
                tipo == "C"
                    ? "EXEC dbo.MalaDiretaConsumo @DataVencimento"
                    : "EXEC dbo.MalaDiretaServico @DataVencimento", parameters);
        }

        public void EnviarMalaDiretaIndividual(string tipo, int malaDiretaId)
        {
            SqlParameter[] parameters =
            {
                new SqlParameter("@Tipo", SqlDbType.Char, 1),
                new SqlParameter("@MalaDiretaId", SqlDbType.Int)
            };

            parameters[0].Value = tipo;
            parameters[1].Value = malaDiretaId;

            Database.ExecuteSqlCommand("EXEC dbo.MalaDiretaIndividual @Tipo, @MalaDiretaId", parameters);
        }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();
            base.OnModelCreating(modelBuilder);
        }

    }
}
