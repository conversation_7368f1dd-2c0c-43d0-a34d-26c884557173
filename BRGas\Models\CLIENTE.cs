﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BRGas.Models
{
    [Table("CLIENTE")]
    public /*partial*/ class CLIENTE
    {
        //[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        //public CLIENTE()
        //{
        //    FLUXO_CLIENTE = new HashSet<FLUXO_CLIENTE>();
        //}

        [Key]
        public int CD_CLIENTE { get; set; }

        public int? CL_CD_FUNCIONARIO { get; set; }

        public int? CL_CD_LOGRADOURO { get; set; }

        [StringLength(5)]
        public string CL_CD_TOPONIMO { get; set; }

        public int? CL_CD_SUBSEGMENTO { get; set; }

        public short? CL_CD_CONSTRUTORA { get; set; }

        public bool CL_EMCONSTRUCAO { get; set; }

        public DateTime? CL_DT_CADASTRO { get; set; }

        [StringLength(60)]
        public string CL_NOME { get; set; }

        public bool CL_CENTRAL_GAS { get; set; }

        public int? CL_NUMERO { get; set; }

        [StringLength(100)]
        public string CL_COMPLEMENTO { get; set; }

        [StringLength(8)]
        public string CL_CEP { get; set; }

        public int? CL_ECONOMIAS { get; set; }

        public int? CL_ESTABELECIMENTOS { get; set; }

        public DateTime? CL_DT_ENTREGA_OBRA { get; set; }

        [Required]
        [StringLength(1)]
        public string CL_TIPO_MEDICAO { get; set; }

        [StringLength(15)]
        public string CL_GIS { get; set; }

        [StringLength(20)]
        public string CL_NUM_DOC { get; set; }

        [Required]
        [StringLength(15)]
        public string CL_AREA { get; set; }

        [StringLength(50)]
        public string CL_EMAIL { get; set; }

        [StringLength(80)]
        public string CL_RAZAO_SOCIAL { get; set; }

        public short? CL_CD_ORIGEM { get; set; }

        public short? CL_CD_POLO { get; set; }

        [StringLength(20)]
        public string CL_IE_ID { get; set; }

        public bool? CL_CONSOME_GLP { get; set; }

        [Column(TypeName = "numeric")]
        public decimal? CL_VOL_GAS_ESTIMADO { get; set; }

        public byte? CL_VENCIMENTO { get; set; }

        public int? CL_CD_SEGMENTO { get; set; }

        public byte? CL_CD_BLOQUEIO { get; set; }

        [StringLength(2)]
        public string CL_TIPO_FLUXO { get; set; }

        public short? CL_CD_ABRIGO { get; set; }

        public bool CL_AQUECEDOR { get; set; }

        public short? CL_CD_OPORTUNIDADE { get; set; }

        public bool CL_ADEQUACAO_APROVADA { get; set; }

        public bool? CL_CONVERTIDO { get; set; }

        [Column(TypeName = "smalldatetime")]
        public DateTime? CL_DT_CAPTACAO { get; set; }

        [Column(TypeName = "smalldatetime")]
        public DateTime? CL_DT_LIGACAO { get; set; }

        public bool? CL_REPROGRAMAR { get; set; }

        public int? CL_PRIORIDADE { get; set; }

        [Column(TypeName = "numeric")]
        public decimal? CL_VOLUME_INICIAL { get; set; }

        [Column(TypeName = "numeric")]
        public decimal? CL_PRESSAO_MEDICAO { get; set; }

        [Column(TypeName = "numeric")]
        public decimal? CL_FATOR_CORRECAO { get; set; }

        public short? CL_CD_VALVULA { get; set; }

        //public short? CL_CD_MEDIDOR { get; set; }

        [StringLength(15)]
        public string CL_PROGRAMACAO { get; set; }

        public bool CL_IN_GARANTIA { get; set; }

        public DateTime? CL_DT_ATIVACAO { get; set; }

        [Column(TypeName = "smalldatetime")]
        public DateTime? CL_DT_TERMINO_GLP { get; set; }

        public double? CL_COORDENADA_LESTE { get; set; }

        public double? CL_COORDENADA_SUL { get; set; }

        [Column(TypeName = "timestamp")]
        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        [MaxLength(8)]
        public byte[] CL_TS_ALTERACAO { get; set; }

        //[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        //public virtual ICollection<FLUXO_CLIENTE> FLUXO_CLIENTE { get; set; }
    }
}
