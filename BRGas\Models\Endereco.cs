﻿using System.ComponentModel.DataAnnotations.Schema;


namespace BRGas.Models
{
    [Table("VW_ENDERECO")]
    public class Endereco
    {
        [Column("CD_CLIENTE")]
        public int Id { get; set; }

        [Column("TM_NOME")]
        public string Tipo<PERSON>og<PERSON>uro { get; set; }

        [Column("LG_NOME")]
        public string Logradouro { get; set; }

        [Column("CL_NUMERO")]
        public int Numero { get; set; }

        [Column("CL_COMPLEMENTO")]
        public string Complemento { get; set; }

        [Column("BR_NOME")]
        public string <PERSON>rro { get; set; }

        [Column("MN_NOME")]
        public string Municipio { get; set; }

        public string Estado => "ES";

        [Column("CL_CEP")]
        public string Cep { get; set; }
    }
}