﻿using System;
using System.ComponentModel.DataAnnotations;

namespace BRGas.Models
{
    public class MalaDireta
    {
        public int Id { get; set; }
        [StringLength(1)]
        public string Tipo { get; set; }
        public DateTime? DataEnvio { get; set; }
        public long Sap { get; set; }
        [StringLength(80)]
        public string RazaoSocial { get; set; }
        [StringLength(50)]
        [Display(Name = "Destinatário")]
        public string Email { get; set; }
        public string Referencia { get; set; }
        [DisplayFormat(ApplyFormatInEditMode = true, DataFormatString = "{0:dd-MM-yyyy}")]
        [Display(Name = "Data Vencimento")]
        [DataType(DataType.Date)]
        public DateTime DataVencimento { get; set; }
        public decimal Valor { get; set; }
        [Display(Name = "Assunto")]
        public string EmailSubject { get; set; }
        [Display(Name = "Conteúdo")]
        public string EmailBody { get; set; }
    }
}