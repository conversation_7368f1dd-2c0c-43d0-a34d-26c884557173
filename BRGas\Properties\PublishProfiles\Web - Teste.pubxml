<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <LastUsedBuildConfiguration>Test</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>http://sgas27as7001.br.com.br/web</SiteUrlToLaunchAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <MSDeployServiceURL>sgas27as7001.br.com.br</MSDeployServiceURL>
    <DeployIisAppPath>GasNatural/Web</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>True</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <UserName>brd\rni6</UserName>
    <_SavePWD>True</_SavePWD>
    <PublishDatabaseSettings>
      <Objects xmlns="">
        <ObjectGroup Name="BRGAS" Order="1" Enabled="False">
          <Destination Path="" />
          <Object Type="DbCodeFirst">
            <Source Path="DBMigration" DbContext="BRGas.Models.BRGasContext, BRGas" MigrationConfiguration="BRGas.Migrations.Configuration, BRGas" Origin="Configuration" />
          </Object>
        </ObjectGroup>
        <ObjectGroup Name="Scada" Order="3" Enabled="False">
          <Destination Path="" />
          <Object Type="DbDacFx">
            <PreSource Path="Data Source=sgas27db0003;Initial Catalog=BR_BaseDados;Integrated Security=False;User ID=sa;Password=**************;Pooling=True" includeData="False" />
            <Source Path="$(IntermediateOutputPath)AutoScripts\Scada_IncrementalSchemaOnly.dacpac" dacpacAction="Deploy" />
          </Object>
          <UpdateFrom Type="Web.Config">
            <Source MatchValue="Data Source=sgas27db0003;Initial Catalog=BR_BaseDados; Integrated security=false; User Id=sa; Password=**************; pooling=true;" MatchAttributes="$(UpdateFromConnectionStringAttributes)" />
          </UpdateFrom>
        </ObjectGroup>
      </Objects>
    </PublishDatabaseSettings>
    <EnableMsDeployAppOffline>False</EnableMsDeployAppOffline>
  </PropertyGroup>
  <ItemGroup>
    <MSDeployParameterValue Include="$(DeployParameterPrefix)BRGAS-Web.config Connection String" />
    <MSDeployParameterValue Include="$(DeployParameterPrefix)Scada-Web.config Connection String" />
  </ItemGroup>
</Project>