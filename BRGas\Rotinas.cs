using brgas.infrastructure.Util;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Web;

namespace BRGasWebx
{
    public class Rotinas : IDisposable
    {
        public string GetRazaoSocial(string CodigoSAP)
        {
            SqlDataReader dr = null;
            string strRet = "";

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandText = "select co_nome from consumidor where co_sap = @Sap";

                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@SAP", CodigoSAP);

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                if (dr.HasRows)
                {
                    dr.Read();
                    strRet = dr[0].ToString();
                }

                dr.<PERSON>();
                comm.Connection.Close();
                return strRet;
            }
        }

        public string getRazaoSocialEmissor(string CodigoSAPEmissor)
        {
            SqlDataReader dr = null;
            string strRet = "";

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandText = "select co_nome_mixedcase from consumidor where CO_SAP_EMISSOR = @Sap";

                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@SAP", CodigoSAPEmissor);

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                if (dr.HasRows)
                {
                    dr.Read();
                    strRet = dr[0].ToString();
                }

                dr.Close();
                comm.Connection.Close();
                return strRet;
            }
        }

        public string getSapRecebedor_via_Emissor(string CodigoSAPEmissor)
        {
            SqlDataReader dr = null;
            string strRet = "";

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandText = "select CO_SAP from consumidor where CO_SAP_EMISSOR = @Sap";

                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@SAP", CodigoSAPEmissor);

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                if (dr.HasRows)
                {
                    dr.Read();
                    strRet = dr[0].ToString();
                }

                dr.Close();
                comm.Connection.Close();
                return strRet;
            }
        }

        public string getRazaoSocialSGC(string CodigoSAP)
        {
            string strRet = "";

            using (SqlCommand comm = new SqlCommand())
            {
                //comm.Connection = new Factory().getGetSQLConnString()();
                comm.Connection = new Factory().GetSQLConnString();

                comm.CommandText = "Select co_nome_mixedcase from consumidor with (nolock) WHERE co_sap = @codigoSAP and co_cd_bloqueio = 1";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("CodigoSAP", CodigoSAP);
                SqlDataReader dr = null;

                if (comm.Connection.State != ConnectionState.Open)
                {
                    comm.Connection.Open();
                }

                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                dr = comm.ExecuteReader(CommandBehavior.CloseConnection);
                if (dr.HasRows)
                {
                    dr.Read();
                    strRet = dr[0].ToString();
                }

                dr.Close();
                comm.Connection.Close();
            }

            return strRet;
        }

        public string getRazaoSocialSGC2(int CodigoSAP)
        {
            string strRet = "";

            if (CodigoSAP > 0)
            {
                using (SqlCommand comm = new SqlCommand())
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.CommandText = "Select co_nome_mixedcase from consumidor with (nolock) WHERE co_sap = @co_sap or co_sap_emissor = @co_sap_emissor";
                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("@co_sap", CodigoSAP);
                    comm.Parameters.AddWithValue("@co_sap_emissor", CodigoSAP);

                    SqlDataReader dr = null;

                    if (comm.Connection.State != ConnectionState.Open)
                    {
                        comm.Connection.Open();
                    }

                    //comm.Connection = new Factory().getGetSQLConnString()();
                    //comm.Connection.Open();

                    dr = comm.ExecuteReader(CommandBehavior.CloseConnection);
                    if (dr.HasRows)
                    {
                        dr.Read();
                        strRet = dr[0].ToString();
                    }

                    comm.Connection.Close();
                    dr.Close();
                }
            }

            return strRet;
        }

        public string getObsLeiturista(string CodigoSAP)
        {
            string strRet = "";

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();

                comm.CommandText = "Select obs_leiturista from clientes with (nolock) where SAP = @SAP";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@SAP", CodigoSAP);
                SqlDataReader dr = null;


                if (comm.Connection.State != ConnectionState.Open)
                {
                    comm.Connection.Open();
                }

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                if (dr.HasRows)
                {
                    dr.Read();
                    strRet = dr[0].ToString();
                }
                dr.Close();
                comm.Connection.Close();
            }
            return strRet;
        }

        public string getObservacaoLeitura(int CodigoSAP, int Subcodigo)
        {
            string strRet = "";

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();

                comm.CommandText = "Select top 1 observacoes from leituras with (nolock) where SAP = @CodigoSAP and subcodigo= @Subcodigo order by Id desc";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("CodigoSAP", CodigoSAP);
                comm.Parameters.AddWithValue("Subcodigo", Subcodigo);

                if (!(comm.Connection.State == ConnectionState.Open))
                {
                    comm.Connection.Open();
                }

                SqlDataReader dr = null;

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                if (dr.HasRows)
                {
                    dr.Read();
                    strRet = dr[0].ToString(); //retorna primeiro campo da primeira linha
                }

                dr.Close();
                comm.Connection.Close();
            }
            return strRet;
        }

        public SqlDataReader getDadosMedidor(int CodigoSAP, int Subcodigo)
        {
            SqlCommand comm = new SqlCommand();

            comm.Connection = new Factory().GetSQLConnString();

            comm.CommandText = "Select medidor, fator_correcao, pressao_medida FROM medidores with (nolock) inner join variaveis with (nolock) on medidores.codigo_variavel=codigo_variavel WHERE clientes.SAP = @CodigoSAP and subcodigo= @Subcodigo";
            comm.Parameters.Clear();
            comm.Parameters.AddWithValue("CodigoSAP", CodigoSAP);
            comm.Parameters.AddWithValue("Subcodigo", Subcodigo);

            if (!(comm.Connection.State == ConnectionState.Open))
            {
                comm.Connection.Open();
            }

            return comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
        }

        public SqlDataReader getDadosPE(int CodigoSAP, int Subcodigo)
        {
            SqlCommand comm = new SqlCommand();

            comm.Connection = new Factory().GetSQLConnString();
            comm.CommandText = "SELECT codigo_variavel, CO_TX_ENDERECO as endereco_concat, localizador, medidor FROM dbo.medidores with (nolock)  inner join CONSUMIDOR with (nolock)  on medidores.sap = CONSUMIDOR.CO_SAP WHERE medidores.SAP = @CodigoSAP and subcodigo=@Subcodigo";
            comm.Parameters.Clear();
            comm.Parameters.AddWithValue("CodigoSAP", CodigoSAP);
            comm.Parameters.AddWithValue("Subcodigo", Subcodigo);

            if (!(comm.Connection.State == ConnectionState.Open))
            {
                comm.Connection.Open();
            }

            return comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
        }

        public SqlDataReader getDadosConsumidor_Localizador(string localizador)
        {
            SqlCommand comm = new SqlCommand();

            comm.Connection = new Factory().GetSQLConnString();
            comm.CommandText = "SELECT co_vencimento FROM DBO.consumidor with (nolock) WHERE co_localizador = @localizador";
            comm.Parameters.Clear();
            comm.Parameters.AddWithValue("localizador", localizador);

            if (!(comm.Connection.State == ConnectionState.Open))
            {
                comm.Connection.Open();
            }

            return comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
        }

        public SqlDataReader getDadosConsumidor_SAP(int codigoSAP)
        {
            SqlCommand comm = new SqlCommand();

            comm.Connection = new Factory().GetSQLConnString();
            comm.CommandText = "SELECT co_vencimento FROM DBO.consumidor with (nolock) WHERE co_sap = @codigoSAP";
            comm.Parameters.Clear();
            comm.Parameters.AddWithValue("CodigoSAP", codigoSAP);

            if (!(comm.Connection.State == ConnectionState.Open))
            {
                comm.Connection.Open();
            }

            return comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
        }

        public string getObservacaoLeitura(int CodigoLeitura)
        {
            string strRet = "";

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.CommandText = "Select observacoes from Leitura with (nolock) where Id = @CodigoLeitura";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("CodigoLeitura", CodigoLeitura);

                if (!(comm.Connection.State == ConnectionState.Open))
                {
                    comm.Connection.Open();
                }

                SqlDataReader dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                if (dr.HasRows)
                {
                    dr.Read();
                    strRet = dr[0].ToString(); //retorna primeiro campo da primeira linha
                }

                dr.Close();
            }

            return strRet;
        }

        public SqlDataReader getMedidoresbySAP(string pCodigo)
        {
            /* 12/08/2020 ROTINA EXCLUSIVA PARA BUSCA VIA SAP  */
            if (string.IsNullOrEmpty(pCodigo))
            {
                throw new Exception("Busca por SAP não informou um número para pesquisar");
            }

            Int64 SAPBigInt = 0;
            if (!Int64.TryParse(pCodigo, out SAPBigInt))
            {
                throw new Exception("Busca por SAP exige um número válido");
            }

            string strCommand = "SELECT CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) + ': ' + isnull(descricao,'<descricao>') + case ativo when 0 then ' [DESATIVADO] ' else '' end + ' - ' + co.co_tx_endereco as endereco, CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) as Valor, CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) + ': ' + case ativo when 0 then 'INATIVO' else 'ATIVO' end + isnull(' - ' + medidor,'') as texto FROM dbo.medidores with (nolock) INNER JOIN dbo.consumidor co with (nolock) ON medidores.SAP = co_sap WHERE 1=1 AND SAP = " + pCodigo.Trim();

            SqlCommand comm = new SqlCommand(strCommand, new Factory().GetSQLConnString());

            if (!(comm.Connection.State == ConnectionState.Open))
            {
                comm.Connection.Open();
            }

            return comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
        }

        public SqlDataReader getMedidoresbyOpcao(string pCodigo, int opcao, int ativo = 1, bool filtroAtivo = true)
        {
            // 1 - SAP
            // 2 - Num. Serie
            // 3 - Nome
            // 4 - Sap ou misto dos outros campos, dependendo do valor do código passado ser um numero ou nao

            if (string.IsNullOrEmpty(pCodigo))
            {
                return null;
            }

            Int64 SAPBigInt = 0;
            if (!Int64.TryParse(pCodigo, out SAPBigInt) && opcao == 1)
            {
                throw new Exception("Busca por SAP exige um número válido");
            }

            string strCommand = "SELECT CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) + ': ' + isnull(descricao,'<descricao>') + case ativo when 0 then ' [DESATIVADO] ' else '' end + ' - ' + co.co_tx_endereco as endereco, CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) as Valor, CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) + ': ' + case ativo when 0 then 'INATIVO' else 'ATIVO' end + isnull(' - ' + medidor,'') as texto FROM dbo.medidores with (nolock) INNER JOIN dbo.consumidor co with (nolock) ON medidores.SAP = co_sap WHERE 1=1 AND ";

            if (opcao == 2)
                strCommand += "medidor like '%" + pCodigo.Trim().ToUpper() + "%'";
            else if (opcao == 3)
                strCommand += "descricao like '%" + pCodigo.Trim() + "%'";
            else if (opcao == 4)
                if (SAPBigInt > 0)
                {
                    strCommand += "SAP = " + SAPBigInt.ToString() + " OR medidor like '%" + pCodigo.Trim().ToUpper() + "%'";
                }
                else
                {
                    strCommand += "(co_nome like '%" + pCodigo.Trim() + "%'" + " OR medidor like '%" + pCodigo.Trim().ToUpper() + "%' OR co_localizador like '" + pCodigo.Trim().ToUpper() + "%)'";
                }
            else
                strCommand += "SAP = " + pCodigo.Trim();

            if (filtroAtivo)
            {
                if (ativo == 1)
                    strCommand += " and ativo = 1";
                else
                    strCommand += " and ativo = 0";
            }
         

            SqlCommand comm = new SqlCommand(strCommand, new Factory().GetSQLConnString());

            if (!(comm.Connection.State == ConnectionState.Open))
            {
                comm.Connection.Open();
            }

            return comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
        }

        public SqlDataReader getMedidoresbyOpcaoReativacao(string termoBusca, int opcaoCampoBusca)
        {
            // 1 - SAP
            // 2 - Num. Serie
            // 3 - Nome

            if (string.IsNullOrEmpty(termoBusca))
            {
                return null;
            }

            string strCommand = "SELECT CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) + ': ' + isnull(descricao,'<descricao>') + case ativo when 0 then ' [DESATIVADO] ' else '' end + ' - ' + co.co_tx_endereco as endereco, CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) as Valor, CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) + ': ' + case ativo when 0 then 'INATIVO' else 'ATIVO' end + isnull(' - ' + medidor,'') as texto FROM dbo.medidores with (nolock) INNER JOIN dbo.consumidor co with (nolock) ON medidores.SAP = co_sap WHERE medidores.Ativo=0 AND ";

            if (opcaoCampoBusca == 2)
                strCommand += "medidor like '%" + termoBusca.Trim().ToUpper() + "%'";
            else if (opcaoCampoBusca == 3)
                strCommand += "descricao like '%" + termoBusca.Trim() + "%'";
            else
                strCommand += "SAP = " + termoBusca.Trim();

            strCommand += " and subcodigo = (select max(subcodigo) from medidores innMed where innMed.sap = medidores.sap)"; //pegar somente quando o último subcódigo for o desligado
            //strCommand += " and not exists(select 1 from medidores innMed2 where innMed2.localizador = medidores.localizador and innMed2.sap <> medidores.sap )"; //Somente se o ponto de consumo estiver livre para reativação. Se houver outro consumidor neste ponto então deve-se usar a troca de titularidade

            SqlCommand comm = new SqlCommand(strCommand, new Factory().GetSQLConnString());

            if (!(comm.Connection.State == ConnectionState.Open))
            {
                comm.Connection.Open();
            }

            return comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
        }
        
        public SqlDataReader getPCEmissor(string pCodigo)
        {
            //Recupera os dados de um ponto de consumo que foi ativado com o código sap do emissor em vez do recebedor
            if (string.IsNullOrEmpty(pCodigo))
            {
                return null;
            }

            string strCommand = "SELECT subcodigo as Valor, cast(subcodigo as varchar) + ': ' + case ativo when 0 then '[INATIVO] - ' else '[ATIVO] - ' end + CO_TX_ENDERECO as texto FROM dbo.medidores LEFT OUTER JOIN dbo.consumidor co ON medidores.SAP = co_sap_emissor WHERE medidores.SAP = " + pCodigo.Trim();

            SqlCommand comm = new SqlCommand(strCommand, new Factory().GetSQLConnString());

            if (!(comm.Connection.State == ConnectionState.Open))
            {
                comm.Connection.Open();
            }

            return comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
        }

        public SqlDataReader getMedidoresCortebyOpcao(string pCodigo, int opcao, int ativo)
        {
            // 1 - SAP
            // 2 - Num. Serie
            // 3 - Nome

            if (string.IsNullOrEmpty(pCodigo))
            {
                return null;
            }

            string strCommand = "Select CAST(SAP AS VARCHAR) +  '-' + cast(subcodigo as varchar) + ': ' + isnull(descricao,'<descricao>') + case ativo when 0 then ' [DESATIVADO] ' else '' end + ' - ' + CO_TX_ENDERECO as endereco, CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) as Valor, CAST(SAP AS VARCHAR(10)) +  '-' + cast(subcodigo as varchar) + ': ' + case ativo when 0 then 'INATIVO' else 'ATIVO' end + isnull(' - ' + medidor,'') as texto FROM dbo.medidores with (nolock) left outer join dbo.consumidor co with (nolock) on medidores.SAP = co_sap where co_cd_bloqueio = 1 AND ";

            if (opcao == 2)
                strCommand += "medidor like '%" + pCodigo.Trim().ToUpper() + "%'";
            else if (opcao == 3)
                strCommand += "descricao like '%" + pCodigo.Trim() + "%'";
            else
                strCommand += "SAP = " + pCodigo;

            if (ativo == 1)
                strCommand += " and ativo = 1";
            else
                strCommand += " and ativo = 0";

            SqlCommand comm = new SqlCommand(strCommand, new Factory().GetSQLConnString());

            if (!(comm.Connection.State == ConnectionState.Open))
            {
                comm.Connection.Open();
            }

            return comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
        }

        public int getCodVarCliente(string pCodigo, int opcaoPesquisa)
        {
            // 1 - SAP
            // 2 - Num. Serie
            // 3 - Nome

            if (string.IsNullOrEmpty(pCodigo))
            {
                return 0;
            }

            string strCommand = "SELECT codigo_variaveis FROM clientes with (nolock) where ";

            if (opcaoPesquisa == 2)
                strCommand += "medidor = '" + pCodigo.ToUpper() + "'";
            else if (opcaoPesquisa == 3)
                strCommand += "descricao = '" + pCodigo + "'";
            else
                strCommand += "SAP = " + pCodigo;

            SqlCommand comm = new SqlCommand();
            comm.Connection = new Factory().GetSQLConnString();
            if (comm.Connection.State != ConnectionState.Open)
            {
                comm.Connection.Open();
            }

            SqlDataReader dr = comm.ExecuteReader(CommandBehavior.CloseConnection);

            dr.Read();

            int intRet = 0;

            if (dr.HasRows)
            {
                intRet = Convert.ToInt16(dr[0]);
            }

            dr.Close();
            comm.Connection.Close();
            comm.Dispose();

            return intRet;
        }

        public int getCodVarConsumidor(string pCodigo, int opcaoPesquisa)
        {
            // 1 - SAP
            // 2 - Num. Serie
            // 3 - Nome

            if (string.IsNullOrEmpty(pCodigo))
            {
                return 0;
            }

            string strCommand = "SELECT isnull(co_cd_codigo_variavel,0) FROM consumidor with (nolock) where ";

            if (opcaoPesquisa == 2)
                strCommand += "CD_CONSUMIDOR = " + pCodigo;
            else if (opcaoPesquisa == 3)
                strCommand += "CO_CD_CLIENTE = " + pCodigo;
            else
                strCommand += "CO_SAP = " + pCodigo;

            int intRet = 0;

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                if (!(comm.Connection.State == ConnectionState.Open))
                {
                    comm.Connection.Open();
                }

                SqlDataReader dr = comm.ExecuteReader(CommandBehavior.CloseConnection);
                dr.Read();

                if (dr.HasRows)
                {
                    intRet = Convert.ToInt16(dr[0]);
                }

                dr.Close();
                comm.Connection.Close();
            }

            return intRet;
        }

        public SqlDataReader getListaManutencao()
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandText = "Select distinct m.CAST(SAP AS VARCHAR(10)) +  '-' + cast(m.subcodigo as varchar) + ': ' + isnull(descricao,'{Sem Descrição}') + ' - ' + isnull(endereco, '{Sem Endereço}') +  ' - ' + isnull(medidor,'{Sem Medidor}') as endereco, m.CAST(SAP AS VARCHAR(10)) +  '-' + cast(m.subcodigo as varchar) as Valor from medidores m with (nolock) inner join medidores_anotacao ma with (nolock) on m.SAP = ma.SAP and m.subcodigo = ma.subcodigo WHERE ma.concluido = 0";

                SqlDataReader dr = null;
                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                return dr;
            }
        }

        public SqlDataReader getLeituras(int pCodigoSAP, int pSubCodigo)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandType = System.Data.CommandType.Text;
                comm.CommandText = "Select cast(Id as varchar) + ' : ' + convert(varchar, inicio, 103) + ' - ' + right(space(10)+CAST(volume as varchar) + ' m3 ',10) + (case when leitura_por_media=1 then '[Média]' else '' end) as info, Id from leitura with (nolock) where SAP = @CodigoSAP and subcodigo = @Subcodigo order by inicio desc";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("CodigoSAP", pCodigoSAP);
                comm.Parameters.AddWithValue("Subcodigo", pSubCodigo);

                SqlDataReader dr = null;

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                return dr;
            }
        }

        public SqlDataReader getObservacoesComplementares(int CodigoLeitura)
        {
            string strSQL = string.Format("Select U.nome Operador, oc.data_registro Registro, oc.texto_obs ObservacaoComplementar, codigo_obs from Observacoes_Complementares OC with (nolock) inner join usuarios U with (nolock) ON OC.codigo_usuario=U.codigo_usuario where codigo_leitura = {0} order by data_registro desc", CodigoLeitura);

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandType = System.Data.CommandType.Text;
                comm.CommandText = strSQL;
                SqlDataReader dr = null;

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                return dr;
            }
        }

        public void InsertObservacaoComplementar(int CodigoLeitura, int CodigoUsuario, string TextoObs)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandType = System.Data.CommandType.StoredProcedure;
                comm.CommandText = "insObservacaoComplementar";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@CodigoLeitura", CodigoLeitura);
                comm.Parameters.AddWithValue("@CodigoUsuario", CodigoUsuario);
                comm.Parameters.AddWithValue("@TextoObs", TextoObs);
                comm.ExecuteNonQuery();
            }
        }

        public void InsObsLeiturista(int SAP, string TextoObs)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandType = System.Data.CommandType.StoredProcedure;
                comm.CommandText = "insObsLeiturista";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@SAP", SAP);
                comm.Parameters.AddWithValue("@TextoObs", TextoObs);
                comm.ExecuteNonQuery();
            }
        }

        public void EnviarRelatorio(string jobID)
        {
            try
            {
                using (SqlCommand comm = new SqlCommand())
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();

                    comm.CommandType = System.Data.CommandType.Text;
                    comm.CommandText = "exec msdb.dbo.sp_start_job @job_name = @jobID";

                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("jobID", jobID);

                    comm.ExecuteNonQuery();
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        public SqlDataReader getRotas()
        {

            try
            {
                using (SqlCommand comm = new SqlCommand())
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();

                    comm.CommandText = "Select descricao, codigo_zona from zonas  with (nolock) ";
                    SqlDataReader dr = null;

                    dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                    return dr;
                }
            }
            catch (Exception)
            {
                throw new HttpUnhandledException("Não foi possível recuperar a lista de Rotas em getRotas()");
            }
        }

        public SqlDataReader getRotas(int CodigoSAP, int Subcodigo)
        {
            try
            {
                using (SqlCommand comm = new SqlCommand())
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();

                    comm.CommandText = "Select descricao, codigo_zona from zonas  with (nolock)  where codigo_zona = (select codigo_zona from clientes  with (nolock) where SAP = @CodigoSAP";
                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("CodigoSAP", CodigoSAP);

                    SqlDataReader dr = null;

                    dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                    return dr;
                }
            }
            catch (Exception)
            {
                throw new HttpUnhandledException($"Não foi possível recuperar as rotas do PC {CodigoSAP.ToString()}-{Subcodigo.ToString()} em getRotas()");
            }
        }

        public SqlDataReader getUsuario(string ChaveRede)
        {
            try
            {
                using (SqlCommand comm = new SqlCommand())
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();

                    comm.CommandText = "SELECT codigo_usuario, nome, tipo FROM usuarios  with (nolock) WHERE upper(chave_rede) = @ChaveRede";
                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("ChaveRede", ChaveRede.ToUpper());

                    SqlDataReader dr = null;

                    dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                    return dr;
                }
            }
            catch (Exception)
            {
                throw new HttpUnhandledException($"Não foi possível recuperar os dados do usuário chave {ChaveRede}");
            }
        }

        public int getCodigoUsuario(string ChaveRede)
        {
            SqlDataReader dr = null;

            try
            {
                dr = getUsuario(ChaveRede);

                if (dr.HasRows)
                {
                    dr.Read();
                    return dr.GetInt32(0);
                }
                else
                {
                    return 0;
                }
            }
            catch (Exception)
            {
                throw new HttpUnhandledException($"Não foi possível recuperar o código do usuário {ChaveRede}");
            }
            finally
            {
                dr.Close();
            }
        }

        public void TrocarMedidor(string codigocliente, int subCodigo, string medidorRetirado, DateTime dataLeitura, float volume,int codigoOperador,string classeVazao,string novoMedidor,float volumeInicial,string observacao,int codigoVar,int tipoOcorrencia,string num_os)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandType = CommandType.StoredProcedure;
                comm.CommandText = "operacao.spTrocarMedidor";

                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@SAP", codigocliente);
                comm.Parameters.AddWithValue("@subcodigo", subCodigo);
                comm.Parameters.AddWithValue("@medidorRetirado", medidorRetirado);
                comm.Parameters.AddWithValue("@DataLeitura", dataLeitura);
                comm.Parameters.AddWithValue("@IndexMedidor", volume);
                comm.Parameters.AddWithValue("@operador", codigoOperador);
                comm.Parameters.AddWithValue("@novoMedidor", novoMedidor);
                comm.Parameters.AddWithValue("@volumeInicial", volumeInicial);
                comm.Parameters.AddWithValue("@obs", observacao);
                comm.Parameters.AddWithValue("@codigoVar", codigoVar);
                comm.Parameters.AddWithValue("@TipoOcorrencia", tipoOcorrencia);
                comm.Parameters.AddWithValue("@num_os", num_os);

                try
                {
                    comm.ExecuteNonQuery();
                }
                catch (Exception exc)
                {
                    throw exc;
                }
            }
        }

        public void TrocarPressao(
            string codigocliente,
            int subCodigo,
            DateTime dataLeitura,
            int indexMedidor,
            int codigoOperador,
            string observacao,
            int codigoVar,
            string num_os)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandType = CommandType.StoredProcedure;
                comm.CommandText = "TrocarPressao";

                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@SAP", codigocliente);
                comm.Parameters.AddWithValue("@subcodigo", subCodigo);
                comm.Parameters.AddWithValue("@DataLeitura", dataLeitura);
                comm.Parameters.AddWithValue("@IndexMedidor", indexMedidor);
                comm.Parameters.AddWithValue("@operador", codigoOperador);
                comm.Parameters.AddWithValue("@obs", observacao);
                comm.Parameters.AddWithValue("@codigoVar", codigoVar);
                comm.Parameters.AddWithValue("@num_os", num_os);

                try
                {
                    comm.ExecuteNonQuery();
                }
                catch (Exception exc)
                {
                    throw exc;
                }
            }
        }

        public SqlDataReader getClientesSGC(
            string strNome,
            string strLogradouro,
            string strApto,
            string strDocumento,
            string strCodigoSap,
            string strCodigoSGC,
            string strBloco,
            string strPorta,
            string strNSMedidor)
        {
            if (string.IsNullOrEmpty(strNome + strLogradouro + strApto + strDocumento + strCodigoSGC + strCodigoSap + strBloco + strPorta + strNSMedidor))
            {
                return null;
            }

            List<string> vetFiltro = new List<string>();

            if (!string.IsNullOrEmpty(strNome))
            {
                vetFiltro.Add(string.Format("(cl_nome like '{0}%' or cl_razao_social like '{0}%' or co_nome like '{0}%')", strNome));
            }

            if (!string.IsNullOrEmpty(strLogradouro))
            {
                vetFiltro.Add(string.Format("(xx_endereco like '%{0}%')", strLogradouro.ToString()));
            }

            if (!string.IsNullOrEmpty(strApto))
            {
                vetFiltro.Add(string.Format("(cl_numero = '{0}')", strApto));
            }

            if (!string.IsNullOrEmpty(strDocumento))
            {
                vetFiltro.Add(string.Format("(co_num_doc_limpo like '{0}%')", strDocumento));
            }

            if (!string.IsNullOrEmpty(strCodigoSap))
            {
                vetFiltro.Add(string.Format("(co_sap in ({0}) or co_sap_emissor in ({0}))", strCodigoSap.Replace(" ", ",")));
            }

            if (!string.IsNullOrEmpty(strCodigoSGC))
            {
                vetFiltro.Add(string.Format("(cl_gis = '{0}')", strCodigoSGC));
            }

            if (!string.IsNullOrEmpty(strBloco))
            {
                vetFiltro.Add(string.Format("(co_bloco = '{0}')", strBloco));
            }

            if (!string.IsNullOrEmpty(strPorta))
            {
                vetFiltro.Add(string.Format("(co_apartamento = '{0}')", strPorta));
            }

            if (!string.IsNullOrEmpty(strNSMedidor))
            {
                if (strNSMedidor.Length < 3)
                    return null;

                vetFiltro.Add(string.Format("(co_num_serie like '{0}%')", strNSMedidor));
            }

            string strFiltro = string.Join(" and ", vetFiltro);
            string strSQL = string.Format("Select * from vw_pesq_atend with (nolock) Where {0} order by localizador", strFiltro);

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandType = System.Data.CommandType.Text;
                comm.CommandText = strSQL;
                SqlDataReader dr = null;

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
               
                return dr;
            }
        }
        public static string FormatarCpfCnpj(string valor)
        {
            valor = valor.Replace(".", "").Replace("/", "").Replace("-", "").Trim();

            if (valor.Length == 11)
            {
                return string.Format("{0}.{1}.{2}-{3}", valor.Substring(0, 3), valor.Substring(3, 3), valor.Substring(6, 3), valor.Substring(9, 2));
            }
            else if (valor.Length == 14)
            {
                return string.Format("{0}.{1}.{2}/{3}-{4}", valor.Substring(0, 2), valor.Substring(2, 3), valor.Substring(5, 3), valor.Substring(8, 4), valor.Substring(12, 2));
            }

            return valor;
        }
        public SqlDataReader listClientesSGC(
            string strNome,
            string strLogradouro,
            string strApto,
            string strDocumento,
            string strCodigoSap,
            string strCodigoSGC,
            string strBloco,
            string strPorta,
            string strNSMedidor,
            string strCodigoBloqueio,
            string strProtocolo)
        {
            if (string.IsNullOrEmpty(strNome + strLogradouro + strApto + strDocumento + strCodigoSGC + strCodigoSap + strBloco + strPorta + strNSMedidor) && string.IsNullOrWhiteSpace(strProtocolo))
            {
                return null;
            }

            List<string> vetFiltro = new List<string>();
            string strSQL = string.Empty;

            if (!string.IsNullOrEmpty(strProtocolo))
            {
                strSQL = string.Format("Select * from vwPesqClientes with (nolock) Where Protocolo ={0} order by localizador", strProtocolo.Trim());
            }
            else
            {
                if (!string.IsNullOrEmpty(strNome))
                {
                    vetFiltro.Add(string.Format("(cl_nome like '%{0}%' or cl_razao_social like '%{0}%' or co_nome like '%{0}%')", strNome.Trim()));
                }

                if (!string.IsNullOrEmpty(strLogradouro))
                {
                    vetFiltro.Add(string.Format("(xx_endereco like '%{0}%')", strLogradouro.Trim()));
                }

                if (!string.IsNullOrEmpty(strApto))
                {
                    vetFiltro.Add(string.Format("(cl_numero = '{0}')", strApto.Trim()));
                }

                if (!string.IsNullOrEmpty(strDocumento))
                {
                    vetFiltro.Add(string.Format("(co_num_doc_limpo like '{0}%' or co_num_doc_limpo like '{1}%')", strDocumento.Replace(".", "").Replace("/", "").Replace("-", ""), strDocumento.Trim()));
                }

                if (!string.IsNullOrEmpty(strCodigoSap))
                {
                    strCodigoSap = strCodigoSap.Trim();
                    vetFiltro.Add(string.Format("(co_sap in ({0}) or co_sap_emissor in ({0}))", strCodigoSap.Trim().Replace(" ", ",")));
                }

                if (!string.IsNullOrEmpty(strCodigoSGC))

                {
                  
                    vetFiltro.Add(string.Format("(cl_gis = '{0}')", strCodigoSGC.Trim()));
                }

                if (!string.IsNullOrEmpty(strBloco))
                {
                    vetFiltro.Add(string.Format("(co_bloco = '{0}')", strBloco.Trim()));
                }

                if (!string.IsNullOrEmpty(strPorta))
                {
                    vetFiltro.Add(string.Format("(co_apartamento = '{0}')", strPorta.Trim()));
                }

                if (!string.IsNullOrEmpty(strNSMedidor))
                {
                    if (strNSMedidor.Length > 3)
                        vetFiltro.Add(string.Format("(co_num_serie like '%{0}%' OR medidor_atual like '%{0}%')", strNSMedidor.Trim()));
                }

                if (!string.IsNullOrEmpty(strCodigoBloqueio) && strCodigoBloqueio != "0")
                {
                    vetFiltro.Add(string.Format("(co_cd_bloqueio = {0})", strCodigoBloqueio));
                }

                string strFiltro = string.Join(" and ", vetFiltro);
                strSQL = string.Format("Select * from vw_pesq_clientes with (nolock) Where {0} order by localizador", strFiltro);
            }

                   
                using (SqlCommand comm = new SqlCommand())
            {
                comm.CommandType = System.Data.CommandType.Text;
                comm.CommandText = strSQL;
                SqlDataReader dr = null;

                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);

                return dr;
            }
        }

        public SqlDataReader getPontosMedicao(
            string strNomeCliente,
            string strLogradouro,
            string strApto,
            string strMedidor,
            string strCodigoSap,
            string strCodigoZona,
            string strCodigoSegmento,
            string strSituacaoLeitura)
        {
            List<string> vetFiltro = new List<string>();

            if (!string.IsNullOrEmpty(strCodigoSegmento) && strCodigoSegmento != "-1")
            {
                if (strCodigoSegmento == "0")
                    vetFiltro.Add("(CD_SEGMENTO = 0 or CD_SEGMENTO is null)");
                else
                    vetFiltro.Add(string.Format("(cd_segmento = {0})", strCodigoSegmento));
            }

            if (strSituacaoLeitura == "3")
                vetFiltro.Add("(ativo is null)");
            else if (strSituacaoLeitura == "1" || strSituacaoLeitura == "0")
                vetFiltro.Add(string.Format("(ativo = {0})", strSituacaoLeitura));
            // 2 = Indiferente, não precisa filtrar

            if (!string.IsNullOrEmpty(strNomeCliente))
            {
                vetFiltro.Add(string.Format("(co_nome like '%{0}%')", strNomeCliente));
            }

            if (!string.IsNullOrEmpty(strLogradouro))
            {
                vetFiltro.Add(string.Format("(upper(endereco) like upper('%{0}%'))", strLogradouro.ToUpper()));
            }

            if (!string.IsNullOrEmpty(strApto))
            {
                vetFiltro.Add(string.Format("(num_logradouro like '{0}' or apto like '{0}' or torre like '{0}')", strApto));
            }

            if (!string.IsNullOrEmpty(strMedidor))
            {
                vetFiltro.Add(string.Format("(medidor like '%{0}%')", strMedidor));
            }

            if (!string.IsNullOrEmpty(strCodigoSap))
            {
                strCodigoSap = strCodigoSap.Trim();

                String[] vetCodigoSap = strCodigoSap.Split(' ');
                strCodigoSap = string.Empty;
                string strLocalizadores = string.Empty;

                for (int i = 0; i < vetCodigoSap.Length; i++)
                {
                    vetCodigoSap[i] = vetCodigoSap[i].Replace(',', ' ').Trim();

                    if (!string.IsNullOrWhiteSpace(vetCodigoSap[i]))
                    {
                        if (vetCodigoSap[i][0] >= 'A' && vetCodigoSap[i][0] <= 'z')
                            strLocalizadores += "'" + vetCodigoSap[i] + "' ";
                        else
                            strCodigoSap += vetCodigoSap[i] + " ";
                    }
                }

                string filtro = string.Empty; //usado para compor o OR quando houver SAP e Localizador na caixa de pesquisa. Antes o vetFiltro usava tudo AND

                if (!string.IsNullOrEmpty(strCodigoSap))
                {
                    //vetFiltro.Add(
                    //    string.Format("(SAP in ({0}))"  //or cpf in ({0})
                    //        , strCodigoSap.Trim().Replace(" ", ","))
                    //    );

                    filtro = string.Format("(SAP in ({0}))", strCodigoSap.Trim().Replace(" ", ","));
                }

                if (!string.IsNullOrEmpty(strLocalizadores))
                {
                    if (string.IsNullOrWhiteSpace(strCodigoSap))
                        vetFiltro.Add(string.Format("(Localizador in ({0}))", strLocalizadores.Trim().Replace(" ", ",")));
                    else
                        filtro = "(" + filtro + string.Format(" OR (Localizador in ({0}))", strLocalizadores.Trim().Replace(" ", ",")) + ")";
                }

                if (!string.IsNullOrWhiteSpace(filtro))
                {
                    vetFiltro.Add(filtro);
                }
            }

            if (!string.IsNullOrEmpty(strCodigoZona) & Convert.ToInt32(strCodigoZona) >= 0)
            {
                vetFiltro.Add(string.Format("(codigo_zona = {0})", strCodigoZona));
            }

            //if (string.IsNullOrWhiteSpace(strDiaFaturamento))
            //{
            //    vetFiltro.Add(" (dia_leitura is null) ");
            //}
            //else if (!string.IsNullOrEmpty(strDiaFaturamento) && Convert.ToInt32(strDiaFaturamento) > 0)
            //{
            //    vetFiltro.Add(string.Format("(dia_leitura = {0})", strDiaFaturamento));
            //}

            string strFiltro = string.Join(" and ", vetFiltro);

            StringBuilder strSQL = new StringBuilder(string.Format("Select * from vw_getPontosEntrega with (nolock) Where {0}", strFiltro)); //Filtros

            using (SqlCommand comm = new SqlCommand())
            {
                comm.CommandType = System.Data.CommandType.Text;
                comm.CommandText = strSQL.ToString();
                SqlDataReader dr = null;

                comm.Connection = new Factory().GetSQLConnString();
                //if (comm.Connection.State != ConnectionState.Open)
                //{
                comm.Connection.Open();
                //}

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);


                return dr;
            }
        }

        public SqlDataReader getPontosMedicaoSemRota(
            string strNomeCliente,
            string strLogradouro,
            string strNumSerie,
            string strCodigoSap)
        {

            List<string> vetFiltro = new List<string>();

            vetFiltro.Add("(CL_CD_SEGMENTO in(1,2,5) or CL_CD_SEGMENTO is null) AND (ativo = 1) AND (codigo_zona = 0)");

            if (!string.IsNullOrEmpty(strNomeCliente))
            {
                vetFiltro.Add(string.Format("(co_nome like '%{0}%')", strNomeCliente));
            }

            if (!string.IsNullOrEmpty(strLogradouro))
            {
                vetFiltro.Add(string.Format("(logradouro like '%{0}%')", strLogradouro.ToUpper()));
            }

            if (!string.IsNullOrEmpty(strNumSerie))
            {
                vetFiltro.Add(string.Format("(medidor like '%{0}%')", strNumSerie));
            }

            if (!string.IsNullOrEmpty(strCodigoSap))
            {
                strCodigoSap = strCodigoSap.Trim();
                vetFiltro.Add(string.Format("(cpf = '{0}' or SAP = {1} or cpf = '{1}')", strCodigoSap, strCodigoSap));
            }

            string strFiltro = string.Join(" and ", vetFiltro);

            StringBuilder strSQL = new StringBuilder(string.Format("Select * from vw_getPontosEntrega with (nolock) Where {0}", strFiltro)); //Filtros

            using (SqlCommand comm = new SqlCommand())
            {
                comm.CommandType = System.Data.CommandType.Text;
                comm.CommandText = strSQL.ToString();
                SqlDataReader dr = null;

                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);

                return dr;
            }
        }

        public void setLogText(string strLogValue)
        {
            //using (SqlCommand comm = new SqlCommand())
            //{
            //    comm.CommandType = System.Data.CommandType.Text;
            //    comm.CommandText = string.Format("Insert into LOG_TESTE(texto) Values ('{0}');", strLogValue);
            //    comm.Connection = new Factory().GetSQLConnString();
            //    comm.Connection.Open();
            //    comm.ExecuteNonQuery();
            //    comm.Connection.Close();
            //}
        }

        public void setPreCalendario(string strCodigoCliente, DateTime dtProxLeitura)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.CommandType = System.Data.CommandType.Text;
                comm.CommandText = string.Format(
                    "Insert into PRE_CALENDARIO(SAP,subcodigo,data_leitura) Values ({0}, 1, Convert(date, '{1}', 103));",
                    strCodigoCliente,
                    //Subcodigo, 
                    dtProxLeitura.ToShortDateString() //Convertido para formato dd/mm/yyyy (103) ante de gravar. Padrao do banco é mm/dd/yyy.
                );

                try
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();
                    comm.ExecuteNonQuery();
                }
                catch (SqlException ex)
                {
                    if (ex.Errors[0].Number != 2601) //PK duplication not treatment required here
                        throw new InvalidOperationException(ex.Message, ex);
                }
            }
        }

        public void InsertLeitura(
            long SAP,
            int subcodigo,
            DateTime data_coleta,
            double leitura_bruta,
            int operador,
            string obs,
            int tipoLeitura,
            int tipoOcorrencia,
            bool bypass)
        {
            if (tipoLeitura < 0 || tipoLeitura > 255)
                tipoLeitura = 0;
            
            if (tipoOcorrencia < 0 || tipoOcorrencia > 255)
                tipoOcorrencia = 0;

            var leitura = new brgas.core.Entities.Leitura
            {
                Sap = SAP,
                SubCodigo = subcodigo,
                DataLeitura = data_coleta,
                IndexMedidor = Convert.ToInt64(leitura_bruta),
                UsuarioId = operador,
                Observacoes = obs,
                TipoLeituraId = (byte?)tipoLeitura,
                TipoOcorrenciaId = (byte)tipoOcorrencia,
                ByPass = bypass,
                NumeroLacre = null,
                NumeroOS = null
            };

            using (var unitOfWork = new brgas.infrastructure.Data.UnitOfWork())
            {
                unitOfWork.LeituraRepository.LeituraAdd(leitura, brgas.core.Enums.GrupoLeitura.Manual);
            }
        }

        public object DesvioLeituraAtual(int SAP, int subcodigo, DateTime data_coleta, float leitura)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                comm.CommandType = System.Data.CommandType.Text;
                comm.CommandText = "Select desv = dbo.getDesvio(@CodigoCliente,@subcodigo,@dataAtual,@leituraAtual)";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@SAP", SAP);
                comm.Parameters.AddWithValue("@subcodigo", subcodigo);
                comm.Parameters.AddWithValue("@dataAtual", data_coleta);
                comm.Parameters.AddWithValue("@leituraAtual", leitura);
                object r = comm.ExecuteScalar();
                comm.Connection.Close();

                return r;
            }
        }

        public DataTable getProgramacaoLeitura(
            DateTime proxima_leitura,
            int codRota = 0,
            string termoBusca = "",
            int subcodigo = 0,
            Boolean avisoOS = true,
            int TopNumLinhas = 25)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                comm.CommandType = CommandType.StoredProcedure;
                comm.CommandText = "getProgramacaoLeituras";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@proxima_leitura", proxima_leitura);
                comm.Parameters.AddWithValue("@codigoRota", codRota);
                comm.Parameters.AddWithValue("@termoBusca", termoBusca);
                comm.Parameters.AddWithValue("@subcodigo", subcodigo);
                comm.Parameters.AddWithValue("@pExibirVerificarOS", Convert.ToInt16(avisoOS));
                comm.Parameters.AddWithValue("@pExibirObsLeiturista", 0); //Exibir somente no relatório
                comm.Parameters.AddWithValue("@pTopRows", TopNumLinhas);
                comm.ExecuteNonQuery();

                SqlDataAdapter da = new SqlDataAdapter(comm);
                DataTable dt = new DataTable();

                da.Fill(dt);
                comm.Connection.Close();
                
                //Filtrar apenas clientes com o tipo de coleta leiturista
                var resultado = dt.Select("TipoColetaId = 1");
                
                return resultado.Length == 0 ? dt.Clone() : resultado.CopyToDataTable();
            }
        }

        public DataTable getCadOperTratamento()
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                comm.CommandType = System.Data.CommandType.StoredProcedure;
                comm.CommandText = "getCadOperTratamento";
                //comm.Parameters.AddWithValue("@proxima_leitura", proxima_leitura);
                comm.ExecuteNonQuery();

                SqlDataAdapter da = new SqlDataAdapter(comm);
                DataTable dt = new DataTable();
                da.Fill(dt);
                comm.Connection.Close();
                return dt;
            }
        }

        public DataTable getSemCodVar()
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                comm.CommandType = System.Data.CommandType.StoredProcedure;
                comm.CommandText = "getSemCodVar";
                //comm.Parameters.AddWithValue("@proxima_leitura", proxima_leitura);
                comm.ExecuteNonQuery();

                SqlDataAdapter da = new SqlDataAdapter(comm);
                DataTable dt = new DataTable();
                da.Fill(dt);
                comm.Connection.Close();
                return dt;
            }
        }

        public SqlDataReader getLeiturasMediasSequenciais(DateTime dataReferencia, int limite, DateTime? ProximaLeitura)
        {
            SqlDataReader dr = null;

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                comm.CommandType = System.Data.CommandType.StoredProcedure;
                comm.CommandText = "getMediasSequenciaisATratar";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@datareferencia", dataReferencia);
                comm.Parameters.AddWithValue("@limite", limite);
                comm.Parameters.AddWithValue("@ProximaLeitura", ProximaLeitura);

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);

                return dr;
            }
        }

        public void delObsComplementares(int codigo_obs)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                comm.CommandType = System.Data.CommandType.Text;
                comm.CommandText = string.Format("delete Observacoes_Complementares where codigo_obs = {0}", codigo_obs);
                comm.ExecuteNonQuery();
                comm.Connection.Close();
            }
        }

        //public void setTratamentoMediaPontoEntrega(Int64 SAP, Int32 subcodigo) //, DateTime data_referencia)
        //{
        //    using (SqlCommand comm = new SqlCommand())
        //    {
        //        comm.Connection = new Factory().GetSQLConnString();
        //        comm.Connection.Open();
        //        comm.CommandType = System.Data.CommandType.Text;
        //        comm.ExecuteNonQuery();
        //        comm.Connection.Close();
        //    }
        //}

        public double getVolumeMedio(long CodigoSAP, int Subcodigo, DateTime DataLeitura)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                comm.CommandText = "dbo.getVolumeMedioPrevisto";  //Baseado na média diária das leituras brutas (index do medidor), não no volume
                comm.CommandType = System.Data.CommandType.StoredProcedure;

                SqlParameter inparm1 = new SqlParameter("@CodigoSAP", System.Data.SqlDbType.BigInt);
                SqlParameter inparm2 = new SqlParameter("@SubCodigo", System.Data.SqlDbType.Int);
                SqlParameter inparm3 = new SqlParameter("@DataPrevistaProximaLeitura", System.Data.SqlDbType.DateTime);
                SqlParameter outparm = new SqlParameter("@RETURN_VALUE", System.Data.SqlDbType.Float);

                inparm1.Direction = System.Data.ParameterDirection.Input;
                inparm2.Direction = System.Data.ParameterDirection.Input;
                inparm3.Direction = System.Data.ParameterDirection.Input;
                outparm.Direction = System.Data.ParameterDirection.ReturnValue;

                inparm1.Value = CodigoSAP;
                inparm2.Value = Subcodigo;
                inparm3.Value = DataLeitura;

                comm.Parameters.Clear();
                comm.Parameters.Add(inparm1);
                comm.Parameters.Add(inparm2);
                comm.Parameters.Add(inparm3);
                comm.Parameters.Add(outparm);

                comm.ExecuteNonQuery();

                double retVal = 0;
                if (outparm.Value != DBNull.Value)
                {
                    retVal = Convert.ToDouble(outparm.Value);
                }
                comm.Connection.Close();
                return retVal;
            }
        }

        public SqlDataReader getManutencoesByLeitura(int codigoLeitura)
        {
            SqlDataReader dr = null;

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                comm.CommandType = System.Data.CommandType.Text;
                comm.CommandText = string.Format("select codigo_manutencao, texto_manutencao, isnull((select 1 from leitura_anotacao with (nolock) where codigo_leitura = {0} and cd_anotacao = codigo_manutencao), 0) marcado from manutencao whith (nolock)", codigoLeitura);
                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                return dr;
            }
        }

        public void setManutencaoAnotacao(int CodigoCliente, int Subcodigo, List<string> itens)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandType = System.Data.CommandType.Text;

                try
                {
                    comm.CommandText = string.Format("UPDATE medidores_anotacao SET concluido = 1 WHERE SAP = {0} and subcodigo = {1} ", CodigoCliente, Subcodigo);
                    comm.ExecuteNonQuery();

                    foreach (string strCodMant in itens)  //Manter marcados como nao concluídos
                    {
                        int RowsAffects = 0;
                        try
                        {
                            comm.CommandText = string.Format("UPDATE medidores_anotacao SET concluido = 0 WHERE SAP = {0} and subcodigo = {2} and cd_anotacao = {1}", CodigoCliente, strCodMant, Subcodigo);
                            RowsAffects = comm.ExecuteNonQuery();

                            if (RowsAffects == 0)
                            {
                                comm.CommandText = string.Format("INSERT medidores_anotacao(SAP, subcodigo, cd_anotacao, data_anotacao, concluido) VALUES ({0},{1},{2}, getdate(), 0)", CodigoCliente, Subcodigo, strCodMant);
                                comm.ExecuteNonQuery();
                            }
                        }
                        catch (Exception)
                        {
                            throw;
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }

                comm.Connection.Close();
            }
        }

        public SqlDataReader getProxLeituraClienteOutraZona(string codigoCliente, int codigoZona)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();

                comm.CommandText = string.Format("SELECT dbo.getProxLeituraClienteOutraZona({0},{1})", codigoCliente, codigoZona);

                return comm.ExecuteReader(); //System.Data.CommandBehavior.CloseConnection
                //comm.Connection.Close();
            }
        }

        public SqlDataReader getProximasLeiturasCalendario(Int16 codigo_zona)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                comm.CommandText = "SELECT dbo.getProxLeituraCalendario(@codigo_zona, GETDATE())";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@codigo_zona", codigo_zona);

                return comm.ExecuteReader(CommandBehavior.CloseConnection);
                //comm.Connection.Close();
            }
        }

        public void FaturasReset(Int64 codPedido)
        {
            try
            {
                using (SqlCommand comm = new SqlCommand())
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();
                    comm.CommandType = System.Data.CommandType.StoredProcedure;
                    comm.CommandText = "sp_reset_faturamento";
                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("@codigo_pedido", codPedido);
                    comm.ExecuteNonQuery();
                    comm.Connection.Close();
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        public SqlDataReader getFaturasReset(int mes, int ano, char status, string codigo)
        {
            SqlDataReader dr = null;

            using (SqlCommand comm = new SqlCommand())
            {
                comm.Connection = new Factory().GetSQLConnString();
                comm.Connection.Open();
                comm.CommandType = System.Data.CommandType.StoredProcedure;
                comm.CommandText = "getFaturasReset";
                comm.Parameters.Clear();
                comm.Parameters.AddWithValue("@mes", mes);
                comm.Parameters.AddWithValue("@ano", ano);
                comm.Parameters.AddWithValue("@status", status);
                comm.Parameters.AddWithValue("@codigo", codigo);

                dr = comm.ExecuteReader(System.Data.CommandBehavior.CloseConnection);
                return dr;
            }
        }

        public void setCalendarioPeriodo(int zona, string dataInicio, string dataFim)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                try
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();
                    comm.CommandType = System.Data.CommandType.StoredProcedure;
                    comm.CommandText = "setCalendario30Dias";
                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("@codigo_zona", zona);
                    comm.Parameters.AddWithValue("@start", DateTime.Parse(dataInicio));
                    comm.Parameters.AddWithValue("@end", DateTime.Parse(dataFim));

                    int rowsAffected = comm.ExecuteNonQuery();
                    comm.Connection.Close();
                }
                catch (Exception)
                {
                    throw;
                }
                comm.Connection.Close();
            }
        }

        public void set_SGC_DesativarConsumidor(
            string codigocliente,
            int subCodigo,
            DateTime dataLeitura,
            float volume,
            int codigoOperador,
            string observacao,
            int tipoOcorrenciaId,
            string chaveRede,
            string OSCorte)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                try
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();

                    comm.Parameters.Clear();

                    comm.CommandType = System.Data.CommandType.StoredProcedure;
                    comm.CommandText = "stp_consumidor_desativar";
                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("@SAP", Convert.ToInt64(codigocliente));
                    comm.Parameters.AddWithValue("@subCodigo", subCodigo);
                    comm.Parameters.AddWithValue("@data_encerramento", dataLeitura);
                    comm.Parameters.AddWithValue("@volume_encerramento", volume);
                    comm.Parameters.AddWithValue("@observacao", observacao);
                    comm.Parameters.AddWithValue("@tipoOcorrenciaId", tipoOcorrenciaId);
                    comm.Parameters.AddWithValue("@chaveRede", chaveRede);
                    comm.Parameters.AddWithValue("@OS_Corte", OSCorte);
                    comm.ExecuteNonQuery();
                    comm.Connection.Close();
                }
                catch (Exception e)
                {
                    throw e;
                }
            }
        }

        public void DesativarConsumidor(
            string codigocliente,
            int subCodigo,
            DateTime dataLeitura,
            float volume,
            string codigoOperador,
            string observacao,
            int tipoOcorrencia,
            string chaveRede,
            String OSCorte)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                try
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();
                    comm.CommandType = System.Data.CommandType.StoredProcedure;
                    comm.CommandText = "DesativarMedidor";

                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("@SAP", codigocliente);
                    comm.Parameters.AddWithValue("@subcodigo", subCodigo);
                    comm.Parameters.AddWithValue("@data_encerramento", dataLeitura);
                    comm.Parameters.AddWithValue("@volume_encerramento", volume);
                    comm.Parameters.AddWithValue("@codigo_operador", codigoOperador);
                    comm.Parameters.AddWithValue("@observacao", observacao);
                    comm.Parameters.AddWithValue("@tipoOcorrenciaId", tipoOcorrencia);
                    comm.Parameters.AddWithValue("@chaveRede", chaveRede);
                    comm.Parameters.AddWithValue("@OS_Corte", OSCorte);
                    comm.ExecuteNonQuery();
                    comm.Connection.Close();
                }
                catch (Exception)
                {
                    throw;
                }
            }
        }

        public void CortarConsumo(
            string codigocliente,
            int subCodigo,
            DateTime dataLeitura,
            float volume,
            int codigoOperador,
            string observacao,
            string chaveRede,
            String OSCorte,
            int bloqueio,
            string lacre)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                try
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();
                    comm.Parameters.Clear();
                    comm.CommandType = System.Data.CommandType.StoredProcedure;
                    comm.CommandText = "stp_consumidor_cortar";

                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("@SAP", codigocliente);
                    comm.Parameters.AddWithValue("@subcodigo", subCodigo);
                    comm.Parameters.AddWithValue("@data_encerramento", dataLeitura);
                    comm.Parameters.AddWithValue("@volume_encerramento", volume);
                    comm.Parameters.AddWithValue("@codigo_operador", codigoOperador);
                    comm.Parameters.AddWithValue("@observacao", observacao);
                    //comm.Parameters.AddWithValue("@tipoOcorrenciaId", tipoOcorrenciaId);
                    comm.Parameters.AddWithValue("@chaveRede", chaveRede);
                    comm.Parameters.AddWithValue("@OS_Corte", OSCorte);
                    comm.Parameters.AddWithValue("@bloqueio", bloqueio);
                    comm.Parameters.AddWithValue("@num_lacre", lacre);
                    comm.ExecuteNonQuery();
                    comm.Connection.Close();
                }
                catch (Exception)
                {
                    throw;
                }
            }
        }

        public void DesligarConsumo(
            string codigocliente,
            int subCodigo,
            DateTime dataLeitura,
            float volume,
            int codigoOperador,
            string observacao,
            int tipoLeituraId,
            string chaveRede,
            String OSCorte,
            int bloqueio,
            string lacre,
            int tipoOcorrenciaId)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                try
                {
                    comm.Connection = new Factory().GetSQLConnString();
                    comm.Connection.Open();

                    comm.CommandType = System.Data.CommandType.StoredProcedure;
                    comm.CommandText = "stp_consumidor_desligar";

                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("@SAP", codigocliente);
                    comm.Parameters.AddWithValue("@subcodigo", subCodigo);
                    comm.Parameters.AddWithValue("@data_encerramento", dataLeitura);
                    comm.Parameters.AddWithValue("@volume_encerramento", volume);
                    comm.Parameters.AddWithValue("@codigo_operador", codigoOperador);
                    comm.Parameters.AddWithValue("@observacao", observacao);
                    comm.Parameters.AddWithValue("@TipoLeituraId", tipoLeituraId);
                    comm.Parameters.AddWithValue("@chaveRede", chaveRede);
                    comm.Parameters.AddWithValue("@OS_Corte", OSCorte);
                    comm.Parameters.AddWithValue("@bloqueio", bloqueio);
                    comm.Parameters.AddWithValue("@lacre", lacre);
                    comm.Parameters.AddWithValue("@tipoOcorrenciaId", tipoOcorrenciaId);
                    comm.ExecuteNonQuery();
                    comm.Connection.Close();
                }
                catch (Exception)
                {
                    throw;
                }
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            //if (disposing)
            //{
            //    if (comm != null)
            //    {
            //        comm.Connection.Close();
            //        comm.Dispose();
            //        comm = null;
            //    }

            //    if (conn != null)
            //    {
            //        conn.Close();
            //        conn.Dispose();
            //        conn = null;
            //    }
            //}
        }

        public void RecalcularProgramacaoPontoEntrega(DateTime DataReferencia)
        {
            using (SqlCommand comm = new SqlCommand())
            {
                try
                {
                    comm.Connection = new Factory().GetSQLConnString("Scada");
                    comm.Connection.Open();

                    comm.CommandType = System.Data.CommandType.StoredProcedure;
                    comm.CommandText = "CalcularQDR";

                    comm.Parameters.Clear();
                    comm.Parameters.AddWithValue("@DataReferenciaEntrada", DataReferencia);

                    comm.ExecuteNonQuery();
                    comm.Connection.Close();
                }
                catch (Exception)
                {
                    throw;
                }
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
            //throw new NotImplementedException();
        }
    }
}