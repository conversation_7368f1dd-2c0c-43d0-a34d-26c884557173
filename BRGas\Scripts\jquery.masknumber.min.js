!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):"object"==typeof exports?module.exports=a(require("jquery")):a(jQuery||Zepto)}(function(a){function b(a,b){var h=a.value;h=c(h),b.integer||(h=d(h,b)),h=e(h,b),h=f(h),g(a,h)}function c(a){return a.replace(/\D/g,"")}function d(a,b){return a=a.replace(/(\d{2})$/,b.decimal.concat("$1")),a=a.replace(/(\d+)(\d{3}, \d{2})$/g,"$1".concat(b.thousands).concat("$2"))}function e(a,b){for(var c=(a.length-3)/3,d=0;c>d;)d++,a=a.replace(/(\d+)(\d{3}.*)/,"$1".concat(b.thousands).concat("$2"));return a}function f(a){return a.replace(/^(0)(\d)/g,"$2")}function g(b,c){b.value!=c&&(b.value=c),a(b).trigger("change",b.value)}a.fn.maskNumber=function(c){var d=a.extend({},a.fn.maskNumber.defaults,c);return d=a.extend(d,c),d=a.extend(d,this.data()),this.keyup(function(){b(this,d)}),this},a.fn.maskNumber.defaults={thousands:",",decimal:".",integer:!1}});