﻿using System;
using System.ComponentModel.DataAnnotations;

namespace BRGas.ViewModels
{
    public class ListConsumidorAtivacao
    {
        public long CodigoConsumidor { get; set; }
        public string LocalizadorConsumidor { get; set; }
        public string NomeConsumidor { get; set; }
        public string Medidor { get; set; }
        public string Bloqueio { get; set; }
        public string ApartamentoPad { get; set; }
        public long? CodigoSAPConsumo { get; set; }
        public string Reguladora { get; set; }
        public string Pressao { get; set; }
        public decimal PressaoMedida { get; set; }

        [DisplayFormat(DataFormatString = "{0:N0}", ApplyFormatInEditMode = true)]
        public decimal? VolumeInicial { get; set; }
        public string Lacre { get; set; }
        [DisplayFormat(ApplyFormatInEditMode = true, DataFormatString = "{0:dd/MM/yyyy}")]
        public DateTime? DataAtivacao { get; set; }
        //[DisplayFormat(ApplyFormatInEditMode =true, DataFormatString = "{0:dd/MM/yyyy}")]
        //public String DataAtivacaoShortString { get; set; }

        public string LogAtivacao { get; set; }
    }
}