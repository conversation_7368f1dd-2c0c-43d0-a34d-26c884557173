using brgas.core.Entities;
using brgas.core.Interfaces.Repositories;
using brgas.core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace brgas.core.Services
{
    public class DesvioService
    {
        private readonly ILeituraRepository _leituraRepository;
        private readonly IDesvioRepository _desvioRepository;
        private readonly IPontoConsumoRepository _consumoRepository;

        public DesvioService(ILeituraRepository leituraRepository, IDesvioRepository desvioRepository, IPontoConsumoRepository pontoConsumoRepository)
        {
            _leituraRepository = leituraRepository;
            _desvioRepository = desvioRepository;
            _consumoRepository = pontoConsumoRepository;
        }

        public void ValidarDesvioLeitura(Leitura leitura, int idUsuario)
        {
            ValidarPontoConsumoAtivo(leitura, idUsuario);

            if (leitura.FatorCorrecao == 0 || leitura.FatorCorrecao == null)
                RegistrarDesvio(leitura, MensagemDesvio.FatorCorrecaoNaoEncontrado, idUsuario);

            Leitura leituraAnterior = _leituraRepository.ObterLeituraAnterior(leitura.Sap, leitura.SubCodigo, leitura.DataLeitura);

            if (leituraAnterior != null)
            {
                if (leitura.DataLeitura != leituraAnterior.DataLeitura.Date)
                {
                    int diasEntreLeituras = (leitura.DataLeitura - leituraAnterior.DataLeitura.Date).Days;

                    if (diasEntreLeituras < 1 || diasEntreLeituras > 45)
                        RegistrarDesvio(leitura, MensagemDesvio.PeriodoMaximoAtingido + diasEntreLeituras, idUsuario);
                }

                var dataLeituraAnterior = (DateTime)leitura.DataLeituraAnterior;
                int diferencaEmMeses = ((leitura.DataLeitura.Year - dataLeituraAnterior.Year) * 12) + leitura.DataLeitura.Month - dataLeituraAnterior.Month;
                if (diferencaEmMeses > 2)
                    RegistrarDesvio(leitura, MensagemDesvio.GrandeIntervaloEntreLeituras + diferencaEmMeses, idUsuario);
            }
        }

        public void ValidarDesvioLeituraOffline(Leitura leitura, int idUsuario)
        {
            var desvios = new List<string>();

            ValidarPontoConsumoAtivo(leitura, idUsuario);
            VerificarRegrasBasicas(leitura, desvios);
            VerificarConsumoMedio(leitura, desvios);
            VerificarLimitesDeConsumo(leitura, desvios);
            VerificarProximaLeitura(leitura, desvios);

            desvios.ForEach(desvio => RegistrarDesvio(leitura, desvio, idUsuario));
        }

        private void VerificarRegrasBasicas(Leitura leitura, List<string> desvios)
        {
            var diasConsumo = leitura.Dias ?? 0;
            if (diasConsumo <= 0) desvios.Add(MensagemDesvio.DataLeiturIgualAnterior);
            if (diasConsumo < 15) desvios.Add(MensagemDesvio.PeriodoLeituraInferior15Dias);
            if (leitura.Volume < 0) desvios.Add(MensagemDesvio.LeituraAtualMenorQueAnterior);
            if (leitura.Volume == leitura.IndexMedidor) desvios.Add(MensagemDesvio.VolumeIgualAnterior);
            if (leitura.DataLeitura > DateTime.Now) desvios.Add(MensagemDesvio.DataLeituraMaiorHoje);
            if (diasConsumo > 45) desvios.Add($"{MensagemDesvio.PeriodoMaximoAtingido} {diasConsumo}");

            var leituraAnterior = _leituraRepository.ObterLeituraAnterior(leitura.Sap, leitura.SubCodigo, leitura.DataLeitura);
            if (leitura.FatorCorrecao != leituraAnterior.FatorCorrecao) desvios.Add(MensagemDesvio.FatorCorrecaoDiferente);

            if (new List<int> { 3, 4 }.Contains(leituraAnterior.TipoOcorrenciaId) && leitura.TipoOcorrenciaId == 2)
                desvios.Add(MensagemDesvio.MedidorTravadoEmbacadoSemAcesso);
        }

        private void VerificarConsumoMedio(Leitura leitura, List<string> desvios)
        {
            var consumoMedioDiario = _leituraRepository.GetMediaConsumoDiario(leitura.Sap, leitura.SubCodigo);
            var diasConsumo = leitura.Dias ?? 0;

            if (consumoMedioDiario > 0)
            {
                var variacao = Math.Abs(((leitura.Volume ?? 0 / diasConsumo) / consumoMedioDiario - 1) * 100);
                if (variacao > 30) desvios.Add(MensagemDesvio.VariacaoConsumoMaior30);
            }
        }

        private void VerificarLimitesDeConsumo(Leitura leitura, List<string> desvios)
        {
            var consumo = (decimal)leitura.Volume;
            if ((leitura.SegmentoId == 1 && consumo > 6) ||
                (leitura.SegmentoId == 2 && consumo > 13) ||
                (leitura.SegmentoId == 5 && consumo > 18))
            {
                desvios.Add(MensagemDesvio.ConsumoSuperiorLimiteSemMedia);
            }
        }

        private void VerificarProximaLeitura(Leitura leitura, List<string> desvios)
        {
            var proximaLeitura = _leituraRepository.GetProximaLeitura(leitura.Sap, leitura.SubCodigo, leitura.DataLeitura);
            if (!proximaLeitura.HasValue) return;

            var diasParaProximaLeitura = (proximaLeitura.Value - DateTime.Now).Days;
            if (diasParaProximaLeitura > 45) desvios.Add(MensagemDesvio.ProximaLeituraMaior45Dias);
            if (diasParaProximaLeitura < 15) desvios.Add(MensagemDesvio.ProximaLeituraMenor15Dias);
        }
        public void ValidarPontoConsumoAtivo(Leitura leitura, int idUsuario)
        {
            var existePontoConsumoAtivo = _consumoRepository.ExistePontoConsumoAtivo(leitura.Sap, leitura.SubCodigo);

            if (!existePontoConsumoAtivo)
                RegistrarDesvio(leitura, MensagemDesvio.SapSemUnidadeOuPontoConsumoAtivo, idUsuario);
        }

        public void RegistrarDesvio(Leitura leitura, string desvio, int idUsuario)
        {
            if (leitura.Sap == 0)
                throw new ArgumentException("SAP não pode ser zero.", nameof(leitura.Sap));

            _desvioRepository.Insert(new Desvio
            {
                Sap = leitura.Sap,
                SubCodigo = leitura.SubCodigo,
                DataLeitura = leitura.DataLeituraAnterior,
                desvio = desvio,
                Operador = idUsuario,
                Observacoes = "Registrado automaticamente no processamento de leitura",
                TipoLeituraId = 1,
                TipoOcorrenciaId = 1,                
                DataRegistro = DateTime.Now.AddSeconds(desvio.Length), //Pra burlar a constraint de unicidade insana que tem nessa tabela
                LeituraId = leitura.Id,
            });
        }

        public void RemoverDesviosLeitura(int leituraId)
        {
            _desvioRepository.DeleteMany(p => p.LeituraId == leituraId);
        }

        public void ValidarDesvioLeituraManual(Leitura leitura, int idUsuario)
        {
            var desvios = new List<string>();

            ValidarPontoConsumoAtivo(leitura, idUsuario);
            VerificarRegrasBasicas(leitura, desvios);
            VerificarConsumoMedio(leitura, desvios);
            VerificarLimitesDeConsumo(leitura, desvios);
            VerificarProximaLeitura(leitura, desvios);

            desvios.ForEach(desvio => RegistrarDesvioComLeitura(leitura, desvio, idUsuario));
        }

        public void RegistrarDesvioSemLeitura(Leitura leitura, string desvio, int idUsuario)
        {
            if (leitura.Sap == 0)
                throw new ArgumentException("SAP não pode ser zero.", nameof(leitura.Sap));

            LimparDesviosAnterioresSemLeitura(leitura.Sap, leitura.SubCodigo);

            _desvioRepository.Insert(new Desvio
            {
                Sap = leitura.Sap,
                SubCodigo = leitura.SubCodigo,
                DataLeitura = leitura.DataLeitura,
                desvio = desvio,
                Operador = idUsuario,
                Observacoes = leitura.Observacoes,
                TipoLeituraId = leitura.TipoLeituraId,
                LeituraBruta = leitura.IndexMedidor,
                NumLacre = leitura.NumeroLacre,
                NumOs = leitura.NumeroOS,
                TipoOcorrenciaId = leitura.TipoOcorrenciaId,
                DataRegistro = DateTime.Now,
                LeituraId = null
            });
        }

        private void RegistrarDesvioComLeitura(Leitura leitura, string desvio, int idUsuario)
        {
            if (leitura.Sap == 0)
                throw new ArgumentException("SAP não pode ser zero.", nameof(leitura.Sap));

            _desvioRepository.Insert(new Desvio
            {
                Sap = leitura.Sap,
                SubCodigo = leitura.SubCodigo,
                DataLeitura = leitura.DataLeitura,
                desvio = desvio,
                Operador = idUsuario,
                Observacoes = "Registrado automaticamente no processamento de leitura manual",
                TipoLeituraId = leitura.TipoLeituraId,
                LeituraBruta = leitura.IndexMedidor,
                TipoOcorrenciaId = leitura.TipoOcorrenciaId,
                DataRegistro = DateTime.Now.AddSeconds(desvio.Length),
                LeituraId = leitura.Id
            });
        }

        public void AssociarDesvioALeitura(long sap, int subCodigo, DateTime dataLeitura, int leituraId, int idUsuarioBypass)
        {
            var desvios = _desvioRepository.Get(d => 
                d.Sap == sap && 
                d.SubCodigo == subCodigo && 
                d.DataLeitura == dataLeitura && 
                d.LeituraId == null);

            foreach (var desvio in desvios)
            {
                desvio.LeituraId = leituraId;
                desvio.IdUsuarioBypass = idUsuarioBypass;
                desvio.DataBypass = DateTime.Now;
                _desvioRepository.Update(desvio);
            }
        }

        public void LimparDesviosAnterioresSemLeitura(long sap, int subCodigo)
        {
            var desviosAnteriores = _desvioRepository.Get(d => 
                d.Sap == sap && 
                d.SubCodigo == subCodigo && 
                d.LeituraId == null);

            foreach (var desvio in desviosAnteriores)
            {
                _desvioRepository.Delete(desvio);
            }
        }

    }
}
