using brgas.core.Dtos;
using brgas.core.Entities;
using brgas.core.Services;
using brgas.infrastructure.Data;
using brgas.infrastructure.Enums;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace brgas.infrastructure.Data
{
    public static class LeituraData
    {
        public static void CalcularPreco(int leituraId)
        {
            using (var _db = new AppDbContext())
            {
                _db.Database.ExecuteSqlCommand($"EXEC fat.CalcularPreco {leituraId}");
            }
        }
        public static void CalcularPrecoIndustrial(int leituraId)
        {
            using (var _db = new AppDbContext())
            {
                _db.Database.ExecuteSqlCommand($"EXEC fat.CalcularPrecoIndustrial {leituraId}");
            }
        }

        public static void AtualizaPrecoIndustrial()
        {
            using (var _db = new AppDbContext())
            {
                _db.Database.ExecuteSqlCommand("EXEC fat.AtualizarPrecosIndustrial");
            }
        }

        public static List<core.Dto.RotasToFirebaseDto> GetRota(DateTime dataLeitura)
        {
            using (AppDbContext _db = new AppDbContext())
            {
                string query = @"
                    SELECT 
                        ROW_NUMBER() OVER(ORDER BY vw.Setor, vw.pad_endereco) AS Ordem,
                        vw.Sap,
                        vw.SubCodigo,
                        vw.CO_LOCALIZADOR AS Localizador,
                        CONVERT(VARCHAR(10), vw.proxima_leitura, 103) AS ProximaLeitura,
                        UPPER(vw.Descricao) AS Descricao,
                        vw.Endereco,
                        vw.medidor AS NumeroSerie,
                        vw.Setor,
                        vw.IndexPrevisto,
                        COALESCE(COALESCE(vw.IndexPrevisto, 0) + ISNULL(l.IndexMedidor, 0), 0) as IndexPrevistoSomado
                    FROM dbo.vw_fieldlist_programacao_leituras vw
                    LEFT JOIN (
                        SELECT 
                            Sap,
                            SubCodigo,
                            IndexMedidor
                        FROM Leitura l1
                        WHERE DataLeitura = (
                            SELECT MAX(DataLeitura)
                            FROM Leitura l2 
                            WHERE l1.Sap = l2.Sap 
                            AND l1.SubCodigo = l2.SubCodigo
                        )
                    ) l ON vw.Sap = l.Sap AND vw.SubCodigo = l.SubCodigo
                    WHERE vw.proxima_leitura = @proxima_leitura";

                var m = _db.Database.SqlQuery<core.Dto.RotasToFirebaseDto>(
                    query,
                    new SqlParameter("@proxima_leitura", dataLeitura));
                return m.ToList();
            }
        }
        public static List<core.Dto.EtiquetasQrcodeDto> GetRotaParaEtiqueta(DateTime dataLeitura)
        {
            using (AppDbContext _db = new AppDbContext())
            {
                string query = @"
                    SELECT Sap, SubCodigo, co_localizador Localizador,
                        UPPER(cl_nome) AS Edificacao, co_apartamento AS Unidade,
                        Endereco, medidor AS NumeroSerie, Setor, bo_nm_bloco AS Bloco
                    FROM dbo.vw_fieldlist_programacao_leituras
                    WHERE proxima_leitura = @proxima_leitura
                    ORDER BY Setor, pad_endereco";

                var m = _db.Database.SqlQuery<core.Dto.EtiquetasQrcodeDto>(
                    query,
                    new SqlParameter("@proxima_leitura", dataLeitura));
                return m.ToList();
            }
        }
        public static List<core.Dto.EtiquetasQrcodeDto> GetRotaParaEtiqueta(string edificacao)
        {
            edificacao = $"%{edificacao}%";
            using (AppDbContext _db = new AppDbContext())
            {
                string query = @"
                    SELECT Sap, SubCodigo, co_localizador Localizador,
                        UPPER(descricao) AS Edificacao, co_apartamento AS Unidade,
                        Endereco, medidor AS NumeroSerie, Setor, bo_nm_bloco AS Bloco
                    FROM dbo.vw_fieldlist_programacao_leituras
                    WHERE co_localizador LIKE @edificacao OR cl_nome LIKE @edificacao
                    ORDER BY Setor, pad_endereco";

                var m = _db.Database.SqlQuery<core.Dto.EtiquetasQrcodeDto>(
                    query,
                    new SqlParameter("@edificacao", edificacao));
                return m.ToList();
            }
        }
        public static List<MedidoresParaLeituraDto> GetMedidoresOffline()
        {
            AppDbContext _db = new AppDbContext();
            string query = @"
                SELECT
                    m.sap,
                    m.subcodigo,
                    c.co_nome AS consumidor,
                    c.co_tx_endereco AS local,
                    m.medidor AS NumeroSerie,
                    v.pressao_medida AS PressaoMedida,
                    d.dia_leitura AS DiaLeitura,
                    v.fator_correcao AS FatorCorrecao,
                    s.SG_NOME AS SegmentoNome,
                    z.abreviacao AS Setor,
                    m.proxima_leitura AS ProximaLeitura,
                    c.CO_CD_CLIENTE EdificacaoId
                FROM
                    dbo.medidores AS m
                    INNER JOIN dbo.dias_leitura AS d ON m.sap = d.sap AND m.subcodigo = d.subcodigo
                    INNER JOIN dbo.CONSUMIDOR AS c ON m.sap = c.CO_SAP
                    INNER JOIN dbo.variaveis AS v ON m.codigo_variavel = v.codigo_variavel
                    LEFT JOIN dbo.segmento AS s ON c.CO_CD_SEGMENTO = s.CD_SEGMENTO
                    LEFT JOIN dbo.zonas AS z ON m.codigo_zona = z.codigo_zona
                WHERE
                    m.ativo = 1
                    AND m.TipoColetaId = 2
                    AND d.dia_leitura > 0
                ORDER BY
                    c.co_nome
            ";

            var result = _db.Database.SqlQuery<MedidoresParaLeituraDto>(query);
            return result.ToList();
        }
        public static List<MedidoresParaLeituraDto> GetMedidoresTelemetria()
        {
            using (AppDbContext _db = new AppDbContext())
            {
                var query = @"
                    SELECT m.sap, m.subcodigo, c.co_nome AS consumidor, c.co_tx_endereco AS local,
                            m.medidor AS NumeroSerie, v.pressao_medida AS PressaoMedida,
                            s.cd_segmento as SegmentoId, 
                            s.sg_nome AS SegmentoNome,
                            m.TagScada,
                            c.CO_CD_CLIENTE AS EdificacaoId,
                            COALESCE(
                                (
                                    SELECT TOP 1 d.desvio
                                    FROM Desvio d
                                    WHERE d.Sap = m.sap AND d.SubCodigo = COALESCE(CASE WHEN m.subcodigo > 1 THEN m.subcodigo ELSE NULL END, 1)
                                    ORDER BY d.DataLeitura DESC
                                ),
                                'Sem Desvio'
                            ) AS Desvio
                    FROM dbo.medidores AS m
                        INNER JOIN dbo.CONSUMIDOR AS c ON m.sap = c.CO_SAP
                        INNER JOIN dbo.variaveis AS v ON m.codigo_variavel = v.codigo_variavel
                        INNER JOIN dbo.SEGMENTO AS s ON c.CO_CD_SEGMENTO = s.CD_SEGMENTO
                    WHERE m.TipoColetaId = 3
                    AND m.ativo = 1
                    AND (m.TagScada IS NOT NULL AND m.TagScada <> '')
                    --AND c.CO_CROMATOGRAFO IS NOT NULL
                    ORDER BY c.co_nome
                ";
                return _db.Database.SqlQuery<MedidoresParaLeituraDto>(query).ToList();
            }
        }

        public static int GetUnidadesTelemetriaSemTag()
        {
            using (AppDbContext _db = new AppDbContext())
            {
                var query = @"
                    SELECT COUNT(DISTINCT c.CD_CONSUMIDOR) AS UnidadesSemTagScada
                    FROM Consumidor c
                    INNER JOIN Medidores m
                        ON m.Sap = c.CO_SAP
                        AND (m.SubCodigo = c.CO_SUBCODIGO OR (m.SubCodigo = 1 AND c.CO_SUBCODIGO IS NULL))
                    WHERE m.TipoColetaId = @TipoColetaId
                      AND m.Ativo = @Ativo
                      AND (m.TagScada IS NULL OR m.TagScada = '')
                ";

                var tipoColetaId = 3;
                var ativo = true;

                return _db.Database.SqlQuery<int>(
                    query,
                    new SqlParameter("@TipoColetaId", tipoColetaId),
                    new SqlParameter("@Ativo", ativo)
                ).First();
            }
        }

        /// <summary>
        /// Insere a leitura
        /// </summary>
        /// <param name="leitura">Objeto do tipo leitura com os dados que serão inseridos</param>
        /// <returns>Retorno objeto com dois campos: 1-desvio: Texto descrevendo o devio 2-Executado: Indica se a leitura foi inserida (Sim/Não)</returns>
        public static LeituraAddRetorno InserirLeitura(Leitura leitura, GrupoLeitura grupoLeitura)
        {
            using (var unitOfWork = new UnitOfWork())
            {
                return unitOfWork.LeituraRepository.LeituraAdd(leitura, (brgas.core.Enums.GrupoLeitura)grupoLeitura);
            }
        }


        public static LeituraAddRetorno InserirLeituraTelemetria(Leitura leitura)
        {
            var desvio = new SqlParameter { ParameterName = "@desvio", SqlDbType = SqlDbType.VarChar, Size = -1, Direction = ParameterDirection.Output };
            var executado = new SqlParameter { ParameterName = "@Executado", SqlDbType = SqlDbType.Bit, Direction = ParameterDirection.Output };
            using (var ctx = new AppDbContext())
            {
                string query = @"
                    EXEC dbo.LeituraTelemetriaAdd 
                        @Sap, 
                        @SubCodigo, 
                        @DataLeitura,
                        @UsuarioId, 
                        @Volume, 
                        @VolumeCorrigido, 
                        @PcsMedio, 
                        @FatorCorrecao, 
                        @TipoOcorrenciaId,
                        @desvio OUT, 
                        @Executado OUT";

                ctx.Database.ExecuteSqlCommand(query,
                    new object[]
                    {
                        new SqlParameter { ParameterName = "@Sap", SqlDbType = SqlDbType.BigInt, Value = leitura.Sap },
                        new SqlParameter { ParameterName = "@Subcodigo", SqlDbType = SqlDbType.Int, Value = leitura.SubCodigo },
                        new SqlParameter { ParameterName = "@DataLeitura", SqlDbType = SqlDbType.DateTime, Value = leitura.DataLeitura },
                        new SqlParameter { ParameterName = "@UsuarioId", SqlDbType = SqlDbType.Int, Value = leitura.UsuarioId },
                        new SqlParameter { ParameterName = "@Volume", SqlDbType = SqlDbType.Decimal, Value = leitura.Volume },
                        new SqlParameter { ParameterName = "@VolumeCorrigido", SqlDbType = SqlDbType.Decimal, Value = leitura.VolumeCorrigido },
                        new SqlParameter { ParameterName = "@PcsMedio", SqlDbType = SqlDbType.Decimal, Value = leitura.Pcs },
                        new SqlParameter { ParameterName = "@FatorCorrecao", SqlDbType = SqlDbType.Decimal, Value = leitura.FatorCorrecao },
                        new SqlParameter { ParameterName = "@TipoOcorrenciaId", SqlDbType = SqlDbType.Int, Value = leitura.TipoOcorrenciaId },
                        desvio,
                        executado
                    }
                );
            }
            return new LeituraAddRetorno
            {
                Desvio = desvio.Value.ToString(),
                Executado = (bool)executado.Value
            };
        }
    }
}
