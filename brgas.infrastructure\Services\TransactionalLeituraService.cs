estusing brgas.core.Dtos;
using brgas.core.Entities;
using brgas.core.Services;
using brgas.infrastructure.Data;
using System;

namespace brgas.infrastructure.Services
{
    public class TransactionalLeituraService
    {
        private readonly Func<UnitOfWork> _unitOfWorkFactory;
        
        public TransactionalLeituraService() : this(() => new UnitOfWork())
        {
        }
        
        public TransactionalLeituraService(Func<UnitOfWork> unitOfWorkFactory)
        {
            _unitOfWorkFactory = unitOfWorkFactory;
        }
        
        public LeituraAddRetorno ProcessarLeitura(Leitura leitura, int usuarioId, brgas.core.Enums.GrupoLeitura grupo)
        {
            using (var unitOfWork = _unitOfWorkFactory())
            {
                    var leituraService = CreateLeituraService(unitOfWork);
                    
                    var resultado = leituraService.ProcessarLeitura(leitura, usuarioId);
                    
                    if (resultado.Success)
                    {
                        unitOfWork.Save();
                    }
                    
                    return new LeituraAddRetorno
                    {
                        Desvio = resultado.Desvio,
                        Executado = resultado.Success && resultado.Executado > 0
                    };
            }
        }
        
        private LeituraManualService CreateLeituraService(UnitOfWork unitOfWork)
        {
            var desvioService = new DesvioService(
                unitOfWork.LeituraRepository,
                unitOfWork.DesvioRepository,
                unitOfWork.PontoConsumoRepository);
                
            var calcularPrecoService = new CalcularPrecoPequenosClientesService(
                unitOfWork.ClasseConsumoRepository,
                unitOfWork.LeituraRepository,
                unitOfWork.UnidadeRepository);
                
            return new LeituraManualService(
                unitOfWork.LeituraRepository,
                unitOfWork.FatorPCSRepository,
                unitOfWork.PontoConsumoRepository,
                unitOfWork.PontoConsumoRepository,
                unitOfWork.UnidadeRepository,
                unitOfWork.FaturaRepository,
                unitOfWork.VariavelRepository,
                unitOfWork.DiasLeituraRepository,
                desvioService,
                calcularPrecoService);
        }
    }
}