using brgas.core.Entities;
using brgas.core.Services;
using brgas.infrastructure.Data;
using brgas.infrastructure.Enums;
using brgas.mvc.Areas.Financeiro.Dtos;
using brgas.mvc.Areas.Financeiro.Models;
using brgas.mvc.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace brgas.mvc.Areas.Financeiro.Services
{
    public class FaturamentoService
    {
        private readonly UnitOfWork _unitOfWork;

        public FaturamentoService()
        {
            _unitOfWork = new UnitOfWork();
        }

        public Result<string> Faturar(ViewModels.FaturamentoViewModel model, int userId)
        {
            var dia = model.DiaFaturamento;
            var enviarParaSap = model.EnviarParaSAP;

            var faturamento = new Faturamento
            {
                EnviarParaSap = enviarParaSap,
                UserId = userId
            };
            faturamento.Faturar(model);
            if (faturamento.QuantidadeProcessada > 0)
            {
                return Result.Ok(faturamento.Mensagem);
            }
            return Result.Fail<string>(faturamento.Mensagem);
        }

        public void CalcularPreco(int leituraId, bool grandeCliente = false, int tipoColetaId = 0)
        {
            if (grandeCliente)
            {
                CalcularPrecoGrandeCliente(leituraId, tipoColetaId);
            } else
            {
                CalcularPrecoPequenosClientes(leituraId);
            }
        }

        private void CalcularPrecoPequenosClientes(int leituraId)
        {
            var calcularPrecoService = new CalcularPrecoPequenosClientesService(
                _unitOfWork.ClasseConsumoRepository,
                _unitOfWork.LeituraRepository,
                _unitOfWork.UnidadeRepository);
            
            calcularPrecoService.Execute(leituraId);
            _unitOfWork.Save();
        }

        public void CalcularPrecoGrandeCliente(int leituraId, int tipoColetaId)
        {
            var leitura = _unitOfWork.LeituraRepository.GetFirstOrDefault(p => p.Id == leituraId);

            if (tipoColetaId == 3) //Telemetria
            {
                var consumosDiario = _unitOfWork.ConsumoDiarioRepository.Get(p => p.LeituraId == leituraId);
                var calcularPrecoGrandeClienteTelemetriaService = 
                    new CalcularPrecoService(_unitOfWork.ClasseConsumoRepository, _unitOfWork.LeituraRepository, _unitOfWork.UnidadeFaturamentoConfigRepository, _unitOfWork.ConsumoDiarioRepository, _unitOfWork.ClasseConsumoGasUltrapassagemRepository);
                calcularPrecoGrandeClienteTelemetriaService.Calcular(leitura, consumosDiario);
            }
            else if (tipoColetaId == 2) //Offline
            {
                var calcularPrecoLeituraOfflineService = new CalcularPrecoLeituraOfflineService(_unitOfWork.ClasseConsumoRepository);
                calcularPrecoLeituraOfflineService.Execute(leitura);
            }
            else
                throw new Exception("Tipo da Coleta inválido para esse cálculo");

            _unitOfWork.Save();
        }

        public void EnviarParaFila(string itens)
        {
            var split = itens.Split(',');
            foreach (var item in split)
            {
                if (!string.IsNullOrWhiteSpace(item))
                {
                    var l = _unitOfWork.LeituraRepository.GetById(int.Parse(item));
                    l.SelecionadaFaturamento = true;
                    _unitOfWork.Save();
                }
            }
        }
        public void RetirarDaFila(string itens)
        {
            var split = itens.Split(',');
            foreach (var item in split)
            {
                if (!string.IsNullOrWhiteSpace(item))
                {
                    var l = _unitOfWork.LeituraRepository.GetById(int.Parse(item));
                    l.SelecionadaFaturamento = false;
                    _unitOfWork.Save();
                }
            }
        }
        public void SelecionarTodosNoDia(int dia)
        {
            using (var ctx = new AppDbContext())
            {
                ctx.FaturamentoSelecionarTodos(dia);
            }
        }
        public void RemoverSelecaoNoDia(int dia)
        {
            using (var ctx = new AppDbContext())
            {
                ctx.FaturamentoRemoverSelecao(dia);
            }
        }
        public IEnumerable<short> ObterDiasDoFaturamento()
        {
            using (var ctx = new AppDbContext())
            {
                var distinctResult = from d in ctx.DiasLeitura
                                     join m in ctx.Leituras on d.Sap equals m.Sap
                                     where d.Dia > 0 && m.StatusFaturamento == (int)StatusFaturamento.Nao_Faturada
                                     group d by d.Dia into uniqueDias
                                     select uniqueDias.FirstOrDefault().Dia.Value;
                return distinctResult.ToList();
            }
        }

        public void SalvarObservacao(int leituraId, string observacao)
        {
            if (observacao != null && observacao.Length > 132)
            {
                observacao = observacao.Substring(0, 132);
            }

            using (var ctx = new AppDbContext())
            {
                var leitura = ctx.Leituras.FirstOrDefault(l => l.Id == leituraId);
                if (leitura != null)
                {
                    leitura.Observacoes = observacao;
                    ctx.SaveChanges();
                }
            }
        }

        #region Mais Informações
        public MaisInfoDto ObterMaisInformacoes(int leituraId)
        {
            var leitura = _unitOfWork.LeituraRepository.GetById(leituraId);
            var unidade = _unitOfWork.UnidadeRepository.Get(i => i.Sap == leitura.Sap).First();
            var log = _unitOfWork.LogFaturamentoRepository.Get(l => l.LeituraId == leituraId).ToList();
            var dataLog = string.Empty;
            if (log.Count > 0)
            {
                dataLog = log.FirstOrDefault().DataHora.ToString();
            }
            var dto = new MaisInfoDto
            {
                Leitura = leitura,
                Edificacao = $"{leitura.Sap} - {leitura.SubCodigo} {unidade.Edificacao.Nome}",
                Unidade = $"{unidade.Localizador.Trim()} - {unidade.Nome}",
                Endereco = unidade.Endereco,
                Segmento = unidade.Segmento.Descricao,
                Log = log ?? null,
                DataLog = dataLog,
            };
            return dto;
        }
        #endregion
        #region Observação
        public Config ObterObservacao()
        {
            return _unitOfWork.ConfigRepository.GetAll().FirstOrDefault();
        }
        internal void GravarObservacao(Config item)
        {
            _unitOfWork.ConfigRepository.Update(item);
            _unitOfWork.Save();
        }
        #endregion
    }
}