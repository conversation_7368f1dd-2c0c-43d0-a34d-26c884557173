<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.CodeCoverage" version="17.12.0" targetFramework="net48" />
  <package id="Microsoft.NET.Test.Sdk" version="17.12.0" targetFramework="net48" />
  <package id="Microsoft.TestPlatform.ObjectModel" version="17.12.0" targetFramework="net48" />
  <package id="MSTest.TestAdapter" version="2.2.10" targetFramework="net48" />
  <package id="MSTest.TestFramework" version="2.2.10" targetFramework="net48" />
  <package id="System.Collections.Immutable" version="1.5.0" targetFramework="net48" />
  <package id="System.Reflection.Metadata" version="1.6.0" targetFramework="net48" />
  <package id="EntityFramework" version="6.4.4" targetFramework="net48" />
  <package id="xunit" version="2.9.3" targetFramework="net48" />
  <package id="xunit.abstractions" version="2.0.3" targetFramework="net48" />
  <package id="xunit.analyzers" version="1.18.0" targetFramework="net48" developmentDependency="true" />
  <package id="xunit.assert" version="2.9.3" targetFramework="net48" />
  <package id="xunit.core" version="2.9.3" targetFramework="net48" />
  <package id="xunit.extensibility.core" version="2.9.3" targetFramework="net48" />
  <package id="xunit.extensibility.execution" version="2.9.3" targetFramework="net48" />
  <package id="xunit.runner.visualstudio" version="3.0.1" targetFramework="net48" developmentDependency="true" />
</packages>